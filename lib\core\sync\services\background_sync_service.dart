import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:workmanager/workmanager.dart';

import '../../utils/logger.dart';
// Context7 MCP: Import unified notification provider for background sync notifications
import '../../notifications/providers/unified_notification_provider.dart';

part 'background_sync_service.g.dart';

/// Service for managing background synchronization
class BackgroundSyncService {
  /// WorkManager task identifier for periodic sync
  static const String periodicSyncTaskId = 'periodic_sync_task';

  /// WorkManager task identifier for immediate sync
  static const String immediateSyncTaskId = 'immediate_sync_task';

  /// Minimum interval between background syncs (in minutes)
  static const int minSyncIntervalMinutes = 15;

  /// Maximum interval between background syncs (in minutes)
  static const int maxSyncIntervalMinutes = 60;

  /// Whether the service is initialized
  bool _isInitialized = false;

  /// Whether the service is in test mode (skips WorkManager operations)
  bool _isTestMode = false;

  /// Stream controller for background sync status
  final _statusController = StreamController<BackgroundSyncStatus>.broadcast();

  /// Current background sync status
  BackgroundSyncStatus _currentStatus = BackgroundSyncStatus.idle();

  /// Initialize the background sync service
  /// Note: Automatic periodic sync is disabled for battery optimization
  /// Sync will only occur when explicitly requested by user actions
  ///
  /// IMPORTANT: WorkManager is now initialized in main.dart with the new
  /// BackgroundTaskDispatcher, so this service no longer initializes WorkManager directly.
  Future<void> initialize({bool isTestMode = false}) async {
    if (_isInitialized) return;

    _isTestMode = isTestMode;

    try {
      // WorkManager initialization is now handled by BackgroundTaskDispatcher in main.dart
      // This service now focuses on sync scheduling and status management

      _isInitialized = true;

      // BATTERY OPTIMIZATION: Do not schedule automatic periodic sync
      // Sync will only happen when user explicitly requests it or when app is actively used
      // await schedulePeriodicSync(); // DISABLED for battery optimization

      _updateStatus(BackgroundSyncStatus.idle());

      AppLogger.info('BackgroundSyncService initialized (using BackgroundTaskDispatcher for execution)');
    } catch (e) {
      AppLogger.error('Failed to initialize BackgroundSyncService', e);
      rethrow;
    }
  }

  /// Dispose the service and clean up resources
  /// Enhanced with error handling to prevent crashes during disposal
  Future<void> dispose() async {
    try {
      // Close status controller safely
      if (!_statusController.isClosed) {
        await _statusController.close();
      }
    } catch (e) {
      AppLogger.error('Error closing status controller in BackgroundSyncService', e);
    }

    try {
      if (_isInitialized && !_isTestMode) {
        // Cancel all scheduled tasks (skip in test mode)
        await Workmanager().cancelAll();
        AppLogger.debug('Cancelled all WorkManager tasks in BackgroundSyncService');
      }
    } catch (e) {
      AppLogger.error('Error cancelling WorkManager tasks in BackgroundSyncService', e);
    }

    try {
      // Reset initialization state
      _isInitialized = false;
      _updateStatus(BackgroundSyncStatus.idle());
    } catch (e) {
      AppLogger.error('Error resetting BackgroundSyncService state', e);
    }
  }

  /// Stream of background sync status updates
  Stream<BackgroundSyncStatus> get statusStream => _statusController.stream;

  /// Current background sync status
  BackgroundSyncStatus get currentStatus => _currentStatus;

  /// Whether the service is initialized
  bool get isInitialized => _isInitialized;

  /// Schedule periodic background sync
  /// BATTERY OPTIMIZATION: This method is now opt-in only and should be used sparingly
  /// Consider using scheduleImmediateSync() for user-triggered actions instead
  Future<void> schedulePeriodicSync({
    int intervalMinutes = minSyncIntervalMinutes,
    bool requiresWifi = false,
    bool requiresCharging = false,
    bool requiresDeviceIdle = false,
    bool userRequested = false, // New parameter to ensure intentional usage
  }) async {
    _ensureInitialized();

    // BATTERY OPTIMIZATION: Only allow periodic sync if explicitly requested by user
    if (!userRequested) {
      AppLogger.warning('Periodic sync not scheduled - user consent required for battery optimization');
      _updateStatus(BackgroundSyncStatus.idle());
      return;
    }

    try {
      // Ensure interval is within bounds (minimum increased for battery optimization)
      final clampedInterval = intervalMinutes.clamp(
        60, // Increased minimum to 1 hour for battery optimization
        maxSyncIntervalMinutes,
      );

      // Skip WorkManager operations in test mode
      if (!_isTestMode) {
        // Cancel existing periodic task
        await Workmanager().cancelByUniqueName(periodicSyncTaskId);

        // Schedule new periodic task with battery-optimized constraints
        // Using BackgroundTaskDispatcher.syncTaskName for consistency
        await Workmanager().registerPeriodicTask(
          periodicSyncTaskId,
          'background_sync', // Use BackgroundTaskDispatcher.syncTaskName
          frequency: Duration(minutes: clampedInterval),
          constraints: Constraints(
            networkType: requiresWifi ? NetworkType.unmetered : NetworkType.connected,
            requiresBatteryNotLow: true, // Always require good battery level
            requiresCharging: requiresCharging,
            requiresDeviceIdle: requiresDeviceIdle,
            requiresStorageNotLow: true,
          ),
          backoffPolicy: BackoffPolicy.exponential,
          backoffPolicyDelay: const Duration(minutes: 5), // Increased backoff delay
          inputData: {
            'sync_type': 'full', // Use new sync_type parameter
            'task_type': 'periodic_sync',
            'scheduled_at': DateTime.now().millisecondsSinceEpoch,
            'user_requested': true,
          },
        );
      }

      _updateStatus(
        BackgroundSyncStatus.scheduled(
          nextSyncTime: DateTime.now().add(Duration(minutes: clampedInterval)),
          intervalMinutes: clampedInterval,
        ),
      );

      AppLogger.info('User-requested periodic sync scheduled every $clampedInterval minutes');
    } catch (e) {
      AppLogger.error('Failed to schedule periodic sync', e);
      _updateStatus(BackgroundSyncStatus.error('Failed to schedule sync: $e'));
      rethrow;
    }
  }

  /// Schedule immediate background sync
  Future<void> scheduleImmediateSync({
    Map<String, dynamic>? inputData,
    bool requiresWifi = false,
    bool requiresCharging = false,
  }) async {
    _ensureInitialized();

    try {
      // Skip WorkManager operations in test mode
      if (!_isTestMode) {
        // Cancel existing immediate task
        await Workmanager().cancelByUniqueName(immediateSyncTaskId);

        // Schedule immediate one-off task
        // Using BackgroundTaskDispatcher.syncTaskName for consistency
        await Workmanager().registerOneOffTask(
          immediateSyncTaskId,
          'background_sync', // Use BackgroundTaskDispatcher.syncTaskName
          constraints: Constraints(
            networkType: requiresWifi ? NetworkType.unmetered : NetworkType.connected,
            requiresBatteryNotLow: false,
            requiresCharging: requiresCharging,
            requiresDeviceIdle: false,
            requiresStorageNotLow: false,
          ),
          backoffPolicy: BackoffPolicy.exponential,
          backoffPolicyDelay: const Duration(seconds: 30),
          inputData: {
            'sync_type': inputData?['sync_type'] ?? 'full', // Use new sync_type parameter
            'task_type': 'immediate_sync',
            'scheduled_at': DateTime.now().millisecondsSinceEpoch,
            ...?inputData,
          },
        );
      }

      _updateStatus(BackgroundSyncStatus.syncing());

      AppLogger.info('Scheduled immediate background sync');
    } catch (e) {
      AppLogger.error('Failed to schedule immediate sync', e);
      _updateStatus(BackgroundSyncStatus.error('Failed to schedule immediate sync: $e'));
      rethrow;
    }
  }

  /// Cancel all scheduled background syncs
  Future<void> cancelAllSyncs() async {
    _ensureInitialized();

    try {
      // Skip WorkManager operations in test mode
      if (!_isTestMode) {
        await Workmanager().cancelAll();
      }

      _updateStatus(BackgroundSyncStatus.cancelled());

      AppLogger.info('Cancelled all background syncs');
    } catch (e) {
      AppLogger.error('Failed to cancel background syncs', e);
      rethrow;
    }
  }

  /// Cancel periodic sync only
  Future<void> cancelPeriodicSync() async {
    _ensureInitialized();

    try {
      // Skip WorkManager operations in test mode
      if (!_isTestMode) {
        await Workmanager().cancelByUniqueName(periodicSyncTaskId);
      }

      AppLogger.info('Cancelled periodic background sync');
    } catch (e) {
      AppLogger.error('Failed to cancel periodic sync', e);
      rethrow;
    }
  }

  /// Check if background sync is supported on this platform
  bool get isSupported {
    // WorkManager is supported on Android and iOS
    return !kIsWeb;
  }

  /// Schedule sync only when app is in foreground (battery optimized)
  /// This is the recommended method for most sync operations
  Future<void> syncWhenAppActive() async {
    _ensureInitialized();

    try {
      // Only sync if app is currently active/visible
      final isAppActive = WidgetsBinding.instance.lifecycleState == AppLifecycleState.resumed;

      if (!isAppActive) {
        AppLogger.debug('Sync skipped - app not active (battery optimization)');
        return;
      }

      // Perform immediate sync without background scheduling
      await scheduleImmediateSync(
        inputData: {'foreground_sync': true, 'battery_optimized': true},
        requiresWifi: false,
        requiresCharging: false,
      );

      AppLogger.info('Foreground sync initiated (battery optimized)');
    } catch (e) {
      AppLogger.error('Failed to perform foreground sync', e);
      rethrow;
    }
  }

  /// Enable periodic sync with user consent (for users who want automatic sync)
  Future<void> enablePeriodicSyncWithUserConsent({
    int intervalMinutes = 60, // Default to 1 hour minimum
    bool requiresWifi = true, // Default to WiFi only for battery optimization
    bool requiresCharging = false,
  }) async {
    AppLogger.info('User enabled periodic sync with $intervalMinutes minute interval');

    await schedulePeriodicSync(
      intervalMinutes: intervalMinutes,
      requiresWifi: requiresWifi,
      requiresCharging: requiresCharging,
      requiresDeviceIdle: false,
      userRequested: true, // User explicitly requested this
    );
  }

  /// Static method for background task callback (used in tests)
  static Future<bool> backgroundTaskCallback() async {
    return await _performBackgroundSync('test_task', null);
  }

  /// Update the current status and notify listeners
  void _updateStatus(BackgroundSyncStatus status) {
    _currentStatus = status;
    _statusController.add(status);
  }

  /// Ensure the service is initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('BackgroundSyncService not initialized. Call initialize() first.');
    }
  }
}

/// Background sync status model
class BackgroundSyncStatus {
  const BackgroundSyncStatus({
    this.state = BackgroundSyncState.idle,
    this.nextSyncTime,
    this.lastSyncTime,
    this.intervalMinutes,
    this.errorMessage,
    this.metadata = const {},
  });

  final BackgroundSyncState state;
  final DateTime? nextSyncTime;
  final DateTime? lastSyncTime;
  final int? intervalMinutes;
  final String? errorMessage;
  final Map<String, dynamic> metadata;

  factory BackgroundSyncStatus.idle() => const BackgroundSyncStatus();

  factory BackgroundSyncStatus.scheduled({required DateTime nextSyncTime, required int intervalMinutes}) =>
      BackgroundSyncStatus(
        state: BackgroundSyncState.scheduled,
        nextSyncTime: nextSyncTime,
        intervalMinutes: intervalMinutes,
      );

  factory BackgroundSyncStatus.syncing() =>
      BackgroundSyncStatus(state: BackgroundSyncState.syncing, lastSyncTime: DateTime.now());

  factory BackgroundSyncStatus.completed() =>
      BackgroundSyncStatus(state: BackgroundSyncState.completed, lastSyncTime: DateTime.now());

  factory BackgroundSyncStatus.error(String message) =>
      BackgroundSyncStatus(state: BackgroundSyncState.error, errorMessage: message);

  factory BackgroundSyncStatus.cancelled() => const BackgroundSyncStatus(state: BackgroundSyncState.cancelled);

  bool get isActive => state == BackgroundSyncState.syncing;
  bool get hasError => state == BackgroundSyncState.error;
  bool get isScheduled => state == BackgroundSyncState.scheduled;

  String get statusMessage {
    switch (state) {
      case BackgroundSyncState.idle:
        return 'Background sync not scheduled';
      case BackgroundSyncState.scheduled:
        if (nextSyncTime != null) {
          final minutes = nextSyncTime!.difference(DateTime.now()).inMinutes;
          return 'Next sync in ${minutes > 0 ? minutes : 0} minutes';
        }
        return 'Background sync scheduled';
      case BackgroundSyncState.syncing:
        return 'Background sync in progress';
      case BackgroundSyncState.completed:
        return 'Background sync completed';
      case BackgroundSyncState.error:
        return 'Background sync error: ${errorMessage ?? "Unknown error"}';
      case BackgroundSyncState.cancelled:
        return 'Background sync cancelled';
    }
  }
}

/// Background sync state enumeration
enum BackgroundSyncState { idle, scheduled, syncing, completed, error, cancelled }

/// Legacy WorkManager callback dispatcher
///
/// DEPRECATED: This callback dispatcher is no longer used.
/// Background tasks are now handled by the BackgroundTaskDispatcher
/// in lib/core/sync/services/background_task_dispatcher.dart
///
/// This is kept for reference and backward compatibility.
/*
@pragma('vm:entry-point')
void callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      AppLogger.info('Background sync task started: $task');

      // Perform the actual sync operation
      final success = await _performBackgroundSync(task, inputData);

      AppLogger.info('Background sync task completed: $task, success: $success');

      return success;
    } catch (e) {
      AppLogger.error('Background sync task failed: $task', e);
      return false;
    }
  });
}
*/

/// Legacy background sync operation
///
/// DEPRECATED: This function is no longer used for actual sync operations.
/// Background sync is now handled by the BackgroundTaskDispatcher
/// in lib/core/sync/services/background_task_dispatcher.dart
///
/// This is kept for reference and backward compatibility.
Future<bool> _performBackgroundSync(String task, Map<String, dynamic>? inputData) async {
  try {
    AppLogger.warning('⚠️ Using deprecated _performBackgroundSync - consider using BackgroundTaskDispatcher');

    // This would typically involve:
    // 1. Initialize necessary services
    // 2. Check network connectivity
    // 3. Process sync queue
    // 4. Handle conflicts
    // 5. Update sync status

    // For now, we'll simulate the sync process
    await Future.delayed(const Duration(seconds: 2));

    // In a real implementation, you would:
    // - Initialize Hive and other services
    // - Get the sync queue service
    // - Process pending operations
    // - Return true if successful, false otherwise

    return true;
  } catch (e) {
    AppLogger.error('Background sync operation failed', e);
    return false;
  }
}

/// Riverpod provider for BackgroundSyncService
@riverpod
BackgroundSyncService backgroundSyncService(Ref ref) {
  final service = BackgroundSyncService();

  // Initialize the service
  service.initialize();

  // Dispose when the provider is disposed
  ref.onDispose(() => service.dispose());

  return service;
}

/// Riverpod provider for background sync status stream
@riverpod
Stream<BackgroundSyncStatus> backgroundSyncStatus(Ref ref) {
  final service = ref.watch(backgroundSyncServiceProvider);
  return service.statusStream;
}
