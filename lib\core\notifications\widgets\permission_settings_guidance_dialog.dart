import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/permission_models.dart' as models;
import '../providers/unified_notification_provider.dart';

/// Permission Settings Guidance Dialog
///
/// **Task 3.3.2: Implement permission request flows with user-friendly messaging**
///
/// This dialog provides step-by-step guidance for users to enable permissions
/// in their device settings when automatic permission requests fail.
///
/// Features:
/// - Platform-specific settings instructions
/// - Visual step-by-step guidance
/// - Direct settings app integration
/// - Follow-up verification
/// - Accessibility support
class PermissionSettingsGuidanceDialog extends ConsumerStatefulWidget {
  final List<PermissionNotificationType> deniedTypes;
  final String? customTitle;
  final String? customDescription;
  final bool showSteps;
  final bool allowOpenSettings;
  final VoidCallback? onSettingsOpened;
  final VoidCallback? onCompleted;

  const PermissionSettingsGuidanceDialog({
    super.key,
    required this.deniedTypes,
    this.customTitle,
    this.customDescription,
    this.showSteps = true,
    this.allowOpenSettings = true,
    this.onSettingsOpened,
    this.onCompleted,
  });

  @override
  ConsumerState<PermissionSettingsGuidanceDialog> createState() => _PermissionSettingsGuidanceDialogState();
}

class _PermissionSettingsGuidanceDialogState extends ConsumerState<PermissionSettingsGuidanceDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  bool _isOpeningSettings = false;
  int _currentStepIndex = 0;
  final List<String> _settingsSteps = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _generateSettingsSteps();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(duration: const Duration(milliseconds: 400), vsync: this);

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeInOut));

    _animationController.forward();
  }

  void _generateSettingsSteps() {
    _settingsSteps.clear();

    // Add platform-specific base steps
    _settingsSteps.addAll([
      'Open your device Settings app',
      'Find and tap "Apps" or "Application Manager"',
      'Scroll down and tap "Masajid Al Bahrain"',
      'Tap "Notifications" or "App notifications"',
    ]);

    // Add permission-specific steps
    for (final type in widget.deniedTypes) {
      switch (type) {
        case PermissionNotificationType.local:
        case PermissionNotificationType.scheduled:
          _settingsSteps.add('Enable "Allow notifications"');
          break;
        case PermissionNotificationType.push:
          _settingsSteps.add('Enable "Push notifications"');
          break;
        case PermissionNotificationType.background:
          _settingsSteps.add('Enable "Background activity"');
          break;
        case PermissionNotificationType.critical:
          _settingsSteps.add('Enable "Critical alerts"');
          break;
        case PermissionNotificationType.provisional:
          _settingsSteps.add('Enable "Provisional notifications"');
          break;
      }
    }

    _settingsSteps.add('Return to the app to continue');
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: AlertDialog(
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            contentPadding: EdgeInsets.zero,
            content: Container(
              width: MediaQuery.of(context).size.width * 0.9,
              constraints: const BoxConstraints(maxWidth: 450),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [_buildHeader(theme), _buildContent(theme), _buildActions(theme)],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
      ),
      child: Column(
        children: [
          // Icon
          Container(
            width: 64,
            height: 64,
            decoration: BoxDecoration(color: theme.colorScheme.primary, borderRadius: BorderRadius.circular(32)),
            child: const Icon(Icons.settings_outlined, size: 32, color: Colors.white),
          ),

          const SizedBox(height: 16),

          // Title
          Text(
            widget.customTitle ?? 'Enable in Settings',
            style: theme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          // Description
          Text(
            widget.customDescription ?? 'To receive notifications, please enable permissions in your device settings.',
            style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurface.withOpacity(0.7)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildContent(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Permission types summary
          _buildPermissionTypesSummary(theme),

          const SizedBox(height: 20),

          // Settings steps
          if (widget.showSteps) _buildSettingsSteps(theme),
        ],
      ),
    );
  }

  Widget _buildPermissionTypesSummary(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: theme.colorScheme.outline.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: theme.colorScheme.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                'Permissions Needed',
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...widget.deniedTypes.map(
            (type) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  const SizedBox(width: 28),
                  Icon(_getIconForPermissionType(type), color: theme.colorScheme.onSurfaceVariant, size: 16),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getDisplayNameForPermissionType(type),
                      style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSteps(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Follow these steps:', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600)),
        const SizedBox(height: 16),
        ...List.generate(_settingsSteps.length, (index) {
          final isCompleted = index < _currentStepIndex;
          final isCurrent = index == _currentStepIndex;

          return _buildStepItem(
            theme,
            index + 1,
            _settingsSteps[index],
            isCompleted: isCompleted,
            isCurrent: isCurrent,
          );
        }),
      ],
    );
  }

  Widget _buildStepItem(
    ThemeData theme,
    int stepNumber,
    String stepText, {
    bool isCompleted = false,
    bool isCurrent = false,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Step number/icon
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: isCompleted
                  ? Colors.green
                  : isCurrent
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline.withOpacity(0.3),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Center(
              child: isCompleted
                  ? const Icon(Icons.check, color: Colors.white, size: 18)
                  : Text(
                      stepNumber.toString(),
                      style: TextStyle(
                        color: isCurrent ? Colors.white : theme.colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
            ),
          ),

          const SizedBox(width: 12),

          // Step text
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                stepText,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: isCompleted
                      ? theme.colorScheme.onSurface.withOpacity(0.6)
                      : isCurrent
                      ? theme.colorScheme.onSurface
                      : theme.colorScheme.onSurface.withOpacity(0.7),
                  fontWeight: isCurrent ? FontWeight.w500 : FontWeight.normal,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Open settings button
          if (widget.allowOpenSettings)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isOpeningSettings ? null : _handleOpenSettings,
                icon: _isOpeningSettings
                    ? const SizedBox(width: 16, height: 16, child: CircularProgressIndicator(strokeWidth: 2))
                    : const Icon(Icons.settings),
                label: Text(
                  _isOpeningSettings ? 'Opening Settings...' : 'Open Settings',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                ),
              ),
            ),

          const SizedBox(height: 12),

          // Manual setup button
          SizedBox(
            width: double.infinity,
            child: TextButton(
              onPressed: _handleManualSetup,
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
              ),
              child: const Text(
                'I\'ll Set It Up Manually',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleOpenSettings() async {
    setState(() {
      _isOpeningSettings = true;
    });

    try {
      // Use unified notification provider to open settings
      // Use unified notification provider to request permissions with settings fallback
      final result = await ref
          .read(unifiedNotificationSettingsProvider.notifier)
          .requestPermissions(
            [], // Empty list since we just want to open settings
            showRationale: false,
            fallbackToSettings: true,
          );

      if (result.isSuccess || result.settingsFallbackUsed == true) {
        widget.onSettingsOpened?.call();
        _startStepProgress();
      } else {
        _showSettingsOpenError();
      }
    } catch (e) {
      _showSettingsOpenError();
    } finally {
      if (mounted) {
        setState(() {
          _isOpeningSettings = false;
        });
      }
    }
  }

  void _handleManualSetup() {
    widget.onCompleted?.call();
    Navigator.of(context).pop(false);
  }

  void _startStepProgress() {
    // Simulate step progression for user guidance
    Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        if (_currentStepIndex < _settingsSteps.length - 1) {
          _currentStepIndex++;
        } else {
          timer.cancel();
        }
      });
    });
  }

  void _showSettingsOpenError() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Unable to open settings. Please navigate manually.'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  IconData _getIconForPermissionType(PermissionNotificationType type) {
    switch (type) {
      case PermissionNotificationType.local:
        return Icons.notifications_outlined;
      case PermissionNotificationType.push:
        return Icons.cloud_queue;
      case PermissionNotificationType.scheduled:
        return Icons.schedule;
      case PermissionNotificationType.background:
        return Icons.layers;
      case PermissionNotificationType.critical:
        return Icons.priority_high;
      case PermissionNotificationType.provisional:
        return Icons.notifications_paused;
    }
  }

  String _getDisplayNameForPermissionType(PermissionNotificationType type) {
    switch (type) {
      case PermissionNotificationType.local:
        return 'Local Notifications';
      case PermissionNotificationType.push:
        return 'Push Notifications';
      case PermissionNotificationType.scheduled:
        return 'Scheduled Notifications';
      case PermissionNotificationType.background:
        return 'Background Notifications';
      case PermissionNotificationType.critical:
        return 'Critical Alerts';
      case PermissionNotificationType.provisional:
        return 'Provisional Notifications';
    }
  }
}
