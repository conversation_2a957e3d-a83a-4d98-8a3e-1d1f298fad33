import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import '../../../../lib/core/notifications/providers/unified_notification_provider.dart';
import '../../../../lib/core/notifications/services/notification_service.dart';
import '../../../../lib/core/notifications/models/notification_settings.dart';
import '../../../../lib/core/notifications/models/prayer_notification_settings.dart';
import '../../../../lib/core/notifications/models/notification_payload.dart';
import '../../../../lib/core/logging/app_logger.dart';
import '../../../../lib/core/storage/storage_service.dart';
import '../../../../lib/features/prayer_times/domain/models/prayer_times.dart';
import '../../../../lib/features/prayer_times/domain/models/prayer_type.dart';
import '../../../helpers/test_helpers.dart';
import '../../../helpers/mock_factory.dart';

import 'notification_accuracy_validation_test.mocks.dart';

/// Context7 MCP: Notification Accuracy Validation Tests
///
/// This test suite validates the accuracy of notification content, timing,
/// and delivery following Context7 MCP best practices.
///
/// **Test Coverage:**
/// - Notification content accuracy
/// - Prayer time calculation accuracy
/// - Payload data integrity
/// - Cross-platform content consistency
/// - Localization accuracy
/// - Data validation and sanitization
///
/// **Context7 MCP Compliance:**
/// - Comprehensive validation patterns
/// - Data integrity verification
/// - Error boundary testing
/// - Performance validation
/// - Resource management
@GenerateMocks([
  NotificationService,
  FlutterLocalNotificationsPlugin,
  StorageService,
])
void main() {
  group('Context7 MCP: Notification Accuracy Validation Tests', () {
    late ProviderContainer container;
    late MockNotificationService mockNotificationService;
    late MockFlutterLocalNotificationsPlugin mockPlugin;
    late MockStorageService mockStorageService;

    /// Context7 MCP: Test setup with comprehensive mock configuration
    setUp(() async {
      // Initialize mocks
      mockNotificationService = MockNotificationService();
      mockPlugin = MockFlutterLocalNotificationsPlugin();
      mockStorageService = MockStorageService();

      // Configure mock behaviors
      when(mockNotificationService.initialize()).thenAnswer((_) async => true);
      when(mockNotificationService.plugin).thenReturn(mockPlugin);

      // Configure storage service with default settings
      when(mockStorageService.getObject<NotificationSettings>(
        'notification_settings',
        any,
      )).thenAnswer((_) async => NotificationSettings.defaultSettings());

      when(mockStorageService.getObject<PrayerNotificationSettings>(
        'prayer_notification_settings',
        any,
      )).thenAnswer((_) async => PrayerNotificationSettings.defaultSettings());

      when(mockStorageService.getInt('notification_settings_migration_version'))
          .thenAnswer((_) async => 1);

      // Create container with overrides
      container = ProviderContainer(
        overrides: [
          storageServiceProvider.overrideWithValue(AsyncValue.data(mockStorageService)),
        ],
      );

      AppLogger.info('🧪 Test setup completed for notification accuracy validation');
    });

    /// Context7 MCP: Proper resource cleanup
    tearDown(() async {
      container.dispose();
      AppLogger.info('🧹 Test cleanup completed');
    });

    group('Notification Content Accuracy', () {
      testWidgets('should generate accurate prayer notification content', (tester) async {
        // Context7 MCP: Test notification content accuracy
        final capturedNotifications = <Map<String, dynamic>>[];

        // Mock notification display to capture content
        when(mockPlugin.zonedSchedule(
          any, any, any, any, any,
          androidScheduleMode: anyNamed('androidScheduleMode'),
          uiLocalNotificationDateInterpretation: anyNamed('uiLocalNotificationDateInterpretation'),
          payload: anyNamed('payload'),
        )).thenAnswer((invocation) async {
          capturedNotifications.add({
            'id': invocation.positionalArguments[0],
            'title': invocation.positionalArguments[1],
            'body': invocation.positionalArguments[2],
            'scheduledDate': invocation.positionalArguments[3],
            'payload': invocation.namedArguments[#payload],
          });
        });

        final manager = await container.read(unifiedNotificationManagerProvider.future);
        
        // Test prayer notification content for different prayer types
        final testPrayerTimes = PrayerTimes(
          date: DateTime.now(),
          fajr: DateTime.now().add(const Duration(hours: 5)),
          dhuhr: DateTime.now().add(const Duration(hours: 12)),
          asr: DateTime.now().add(const Duration(hours: 15)),
          maghrib: DateTime.now().add(const Duration(hours: 18)),
          isha: DateTime.now().add(const Duration(hours: 20)),
          sunrise: DateTime.now().add(const Duration(hours: 6)),
          latitude: 25.276987,
          longitude: 55.296249,
        );

        await manager.schedulePrayerNotifications(
          date: testPrayerTimes.date,
          latitude: testPrayerTimes.latitude,
          longitude: testPrayerTimes.longitude,
        );

        await tester.pump(const Duration(milliseconds: 100));

        // Verify notification content accuracy
        expect(capturedNotifications.isNotEmpty, isTrue,
          reason: 'Prayer notifications should be generated');

        for (final notification in capturedNotifications) {
          final title = notification['title'] as String?;
          final body = notification['body'] as String?;
          final payload = notification['payload'] as String?;

          // Context7 MCP: Validate notification content structure
          expect(title, isNotNull, reason: 'Notification title should not be null');
          expect(body, isNotNull, reason: 'Notification body should not be null');
          expect(title!.isNotEmpty, isTrue, reason: 'Notification title should not be empty');
          expect(body!.isNotEmpty, isTrue, reason: 'Notification body should not be empty');

          // Validate payload structure if present
          if (payload != null && payload.isNotEmpty) {
            expect(() => jsonDecode(payload), returnsNormally,
              reason: 'Notification payload should be valid JSON');
            
            final payloadData = jsonDecode(payload) as Map<String, dynamic>;
            expect(payloadData.containsKey('prayer_type'), isTrue,
              reason: 'Payload should contain prayer type information');
            expect(payloadData.containsKey('scheduled_time'), isTrue,
              reason: 'Payload should contain scheduled time information');
          }

          // Validate prayer-specific content
          final expectedPrayerNames = ['Fajr', 'Dhuhr', 'Asr', 'Maghrib', 'Isha'];
          final containsValidPrayerName = expectedPrayerNames.any((name) => 
            title.contains(name) || body.contains(name));
          
          expect(containsValidPrayerName, isTrue,
            reason: 'Notification should contain valid prayer name');
        }

        AppLogger.info('✅ Notification content accuracy test passed');
      });

      testWidgets('should handle special characters and localization correctly', (tester) async {
        // Context7 MCP: Test localization and special character handling
        final capturedContent = <String>[];

        when(mockPlugin.zonedSchedule(
          any, any, any, any, any,
          androidScheduleMode: anyNamed('androidScheduleMode'),
          uiLocalNotificationDateInterpretation: anyNamed('uiLocalNotificationDateInterpretation'),
        )).thenAnswer((invocation) async {
          final title = invocation.positionalArguments[1] as String?;
          final body = invocation.positionalArguments[2] as String?;
          if (title != null) capturedContent.add(title);
          if (body != null) capturedContent.add(body);
        });

        final manager = await container.read(unifiedNotificationManagerProvider.future);
        
        // Test with various special characters and Unicode
        await manager.schedulePrayerNotifications(
          date: DateTime.now(),
          latitude: 25.276987,
          longitude: 55.296249,
        );

        await tester.pump(const Duration(milliseconds: 100));

        // Verify special character handling
        for (final content in capturedContent) {
          // Context7 MCP: Validate Unicode and special character support
          expect(content.codeUnits.every((unit) => unit >= 0 && unit <= 0x10FFFF), isTrue,
            reason: 'Content should contain valid Unicode characters');
          
          // Validate no HTML entities or unescaped characters
          expect(content.contains('&lt;'), isFalse,
            reason: 'Content should not contain HTML entities');
          expect(content.contains('&gt;'), isFalse,
            reason: 'Content should not contain HTML entities');
          expect(content.contains('&amp;'), isFalse,
            reason: 'Content should not contain HTML entities');
        }

        AppLogger.info('✅ Special characters and localization test passed');
      });
    });

    group('Prayer Time Calculation Accuracy', () {
      testWidgets('should calculate prayer times with high precision', (tester) async {
        // Context7 MCP: Test prayer time calculation accuracy
        final capturedScheduleTimes = <DateTime>[];

        when(mockPlugin.zonedSchedule(
          any, any, any, any, any,
          androidScheduleMode: anyNamed('androidScheduleMode'),
          uiLocalNotificationDateInterpretation: anyNamed('uiLocalNotificationDateInterpretation'),
        )).thenAnswer((invocation) async {
          final scheduledDate = invocation.positionalArguments[3] as DateTime;
          capturedScheduleTimes.add(scheduledDate);
        });

        final manager = await container.read(unifiedNotificationManagerProvider.future);
        
        // Test prayer time calculations for different locations
        final testLocations = [
          {'lat': 25.276987, 'lng': 55.296249, 'name': 'Dubai'},
          {'lat': 21.3891, 'lng': 39.8579, 'name': 'Mecca'},
          {'lat': 40.7128, 'lng': -74.0060, 'name': 'New York'},
          {'lat': 51.5074, 'lng': -0.1278, 'name': 'London'},
        ];

        for (final location in testLocations) {
          capturedScheduleTimes.clear();
          
          await manager.schedulePrayerNotifications(
            date: DateTime.now(),
            latitude: location['lat'] as double,
            longitude: location['lng'] as double,
          );

          await tester.pump(const Duration(milliseconds: 50));

          // Verify prayer times are calculated
          expect(capturedScheduleTimes.isNotEmpty, isTrue,
            reason: 'Prayer times should be calculated for ${location['name']}');

          // Verify times are in chronological order (for same day)
          if (capturedScheduleTimes.length > 1) {
            final sortedTimes = List<DateTime>.from(capturedScheduleTimes)..sort();
            expect(capturedScheduleTimes, equals(sortedTimes),
              reason: 'Prayer times should be in chronological order for ${location['name']}');
          }

          // Verify times are reasonable (within 24 hours from now)
          final now = DateTime.now();
          for (final time in capturedScheduleTimes) {
            final timeDifference = time.difference(now);
            expect(timeDifference.inHours, lessThanOrEqualTo(24),
              reason: 'Prayer times should be within 24 hours for ${location['name']}');
            expect(timeDifference.inHours, greaterThanOrEqualTo(-1),
              reason: 'Prayer times should not be in the past for ${location['name']}');
          }
        }

        AppLogger.info('✅ Prayer time calculation accuracy test passed');
      });

      testWidgets('should handle edge cases in prayer time calculations', (tester) async {
        // Context7 MCP: Test edge cases in prayer time calculations
        final edgeCaseLocations = [
          {'lat': 90.0, 'lng': 0.0, 'name': 'North Pole'},
          {'lat': -90.0, 'lng': 0.0, 'name': 'South Pole'},
          {'lat': 0.0, 'lng': 180.0, 'name': 'International Date Line'},
          {'lat': 0.0, 'lng': -180.0, 'name': 'International Date Line West'},
        ];

        final manager = await container.read(unifiedNotificationManagerProvider.future);

        for (final location in edgeCaseLocations) {
          // Test should not throw exceptions for edge case locations
          expect(() async {
            await manager.schedulePrayerNotifications(
              date: DateTime.now(),
              latitude: location['lat'] as double,
              longitude: location['lng'] as double,
            );
          }, returnsNormally,
            reason: 'Prayer time calculation should handle edge case: ${location['name']}');
        }

        AppLogger.info('✅ Prayer time calculation edge cases test passed');
      });
    });

    group('Payload Data Integrity', () {
      testWidgets('should maintain payload data integrity', (tester) async {
        // Context7 MCP: Test payload data integrity
        final capturedPayloads = <String>[];

        when(mockPlugin.zonedSchedule(
          any, any, any, any, any,
          androidScheduleMode: anyNamed('androidScheduleMode'),
          uiLocalNotificationDateInterpretation: anyNamed('uiLocalNotificationDateInterpretation'),
          payload: anyNamed('payload'),
        )).thenAnswer((invocation) async {
          final payload = invocation.namedArguments[#payload] as String?;
          if (payload != null) capturedPayloads.add(payload);
        });

        final manager = await container.read(unifiedNotificationManagerProvider.future);
        
        await manager.schedulePrayerNotifications(
          date: DateTime.now(),
          latitude: 25.276987,
          longitude: 55.296249,
        );

        await tester.pump(const Duration(milliseconds: 100));

        // Verify payload integrity
        for (final payload in capturedPayloads) {
          // Context7 MCP: Validate JSON structure
          expect(() => jsonDecode(payload), returnsNormally,
            reason: 'Payload should be valid JSON');

          final payloadData = jsonDecode(payload) as Map<String, dynamic>;

          // Validate required fields
          expect(payloadData.containsKey('prayer_type'), isTrue,
            reason: 'Payload should contain prayer_type');
          expect(payloadData.containsKey('scheduled_time'), isTrue,
            reason: 'Payload should contain scheduled_time');
          expect(payloadData.containsKey('location'), isTrue,
            reason: 'Payload should contain location');

          // Validate data types
          expect(payloadData['prayer_type'], isA<String>(),
            reason: 'prayer_type should be a string');
          expect(payloadData['scheduled_time'], isA<String>(),
            reason: 'scheduled_time should be a string');
          expect(payloadData['location'], isA<Map>(),
            reason: 'location should be a map');

          // Validate location data
          final location = payloadData['location'] as Map<String, dynamic>;
          expect(location.containsKey('latitude'), isTrue,
            reason: 'Location should contain latitude');
          expect(location.containsKey('longitude'), isTrue,
            reason: 'Location should contain longitude');
          expect(location['latitude'], isA<num>(),
            reason: 'Latitude should be a number');
          expect(location['longitude'], isA<num>(),
            reason: 'Longitude should be a number');

          // Validate coordinate ranges
          final latitude = location['latitude'] as num;
          final longitude = location['longitude'] as num;
          expect(latitude, greaterThanOrEqualTo(-90),
            reason: 'Latitude should be >= -90');
          expect(latitude, lessThanOrEqualTo(90),
            reason: 'Latitude should be <= 90');
          expect(longitude, greaterThanOrEqualTo(-180),
            reason: 'Longitude should be >= -180');
          expect(longitude, lessThanOrEqualTo(180),
            reason: 'Longitude should be <= 180');
        }

        AppLogger.info('✅ Payload data integrity test passed');
      });
    });

    group('Cross-Platform Content Consistency', () {
      testWidgets('should maintain consistent content across platforms', (tester) async {
        // Context7 MCP: Test cross-platform content consistency
        final platformNotifications = <String, List<Map<String, dynamic>>>{
          'android': [],
          'ios': [],
        };

        // Mock platform-specific notification capture
        when(mockPlugin.zonedSchedule(
          any, any, any, any, any,
          androidScheduleMode: anyNamed('androidScheduleMode'),
          uiLocalNotificationDateInterpretation: anyNamed('uiLocalNotificationDateInterpretation'),
        )).thenAnswer((invocation) async {
          final notification = {
            'title': invocation.positionalArguments[1],
            'body': invocation.positionalArguments[2],
            'scheduledDate': invocation.positionalArguments[3],
          };
          
          // Simulate platform-specific handling
          platformNotifications['android']!.add(notification);
          platformNotifications['ios']!.add(notification);
        });

        final manager = await container.read(unifiedNotificationManagerProvider.future);
        
        await manager.schedulePrayerNotifications(
          date: DateTime.now(),
          latitude: 25.276987,
          longitude: 55.296249,
        );

        await tester.pump(const Duration(milliseconds: 100));

        // Verify cross-platform consistency
        final androidNotifications = platformNotifications['android']!;
        final iosNotifications = platformNotifications['ios']!;

        expect(androidNotifications.length, equals(iosNotifications.length),
          reason: 'Same number of notifications should be generated for both platforms');

        for (int i = 0; i < androidNotifications.length; i++) {
          final androidNotif = androidNotifications[i];
          final iosNotif = iosNotifications[i];

          // Context7 MCP: Verify content consistency
          expect(androidNotif['title'], equals(iosNotif['title']),
            reason: 'Notification titles should be consistent across platforms');
          expect(androidNotif['body'], equals(iosNotif['body']),
            reason: 'Notification bodies should be consistent across platforms');
          expect(androidNotif['scheduledDate'], equals(iosNotif['scheduledDate']),
            reason: 'Scheduled dates should be consistent across platforms');
        }

        AppLogger.info('✅ Cross-platform content consistency test passed');
      });
    });
  });
}
