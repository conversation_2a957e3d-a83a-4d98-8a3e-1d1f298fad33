import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'provider_router.dart';
import 'deployment_config.dart';
import '../../logging/app_logger.dart';

part 'main_app_adapter.g.dart';

/// Main Application Adapter for Notification System Migration
///
/// **Context7 MCP Implementation:**
/// - Provides transparent routing for main application components
/// - Ensures backward compatibility during migration
/// - Handles graceful fallback to legacy providers
/// - Monitors adapter performance and health
/// - Supports emergency rollback scenarios
///
/// **Usage in Main Application:**
/// ```dart
/// // Instead of directly using legacy providers:
/// // final settings = ref.watch(legacyPrayerNotificationProvider);
/// 
/// // Use the adapter for transparent routing:
/// final settings = ref.watch(mainAppNotificationSettingsProvider);
/// final manager = ref.watch(mainAppNotificationManagerProvider);
/// ```
@riverpod
Future<MainAppNotificationSettings> mainAppNotificationSettings(MainAppNotificationSettingsRef ref) async {
  try {
    AppLogger.debug('MainAppAdapter: Routing notification settings request');

    // Get deployment configuration
    final deploymentConfig = await ref.watch(deploymentConfigProvider.future);
    
    if (deploymentConfig.shouldUseUnifiedProvider) {
      // Route to unified provider through router
      final unifiedSettings = await ref.watch(routedNotificationSettingsProvider.future);
      
      AppLogger.debug('MainAppAdapter: Using unified notification settings');
      return MainAppNotificationSettings.fromUnified(unifiedSettings);
      
    } else {
      // Use legacy provider directly
      AppLogger.debug('MainAppAdapter: Using legacy notification settings');
      return MainAppNotificationSettings.legacy();
    }

  } catch (e, stackTrace) {
    AppLogger.error(
      'MainAppAdapter: Settings routing failed',
      context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
    );

    // Emergency fallback to safe defaults
    return MainAppNotificationSettings.safe();
  }
}

/// Main Application Notification Manager Provider
@riverpod
Future<MainAppNotificationManager> mainAppNotificationManager(MainAppNotificationManagerRef ref) async {
  try {
    AppLogger.debug('MainAppAdapter: Routing notification manager request');

    // Get deployment configuration
    final deploymentConfig = await ref.watch(deploymentConfigProvider.future);
    
    if (deploymentConfig.shouldUseUnifiedProvider) {
      // Route to unified provider through router
      final unifiedManager = await ref.watch(routedNotificationManagerProvider.future);
      
      AppLogger.debug('MainAppAdapter: Using unified notification manager');
      return MainAppNotificationManager.fromUnified(unifiedManager);
      
    } else {
      // Use legacy manager
      AppLogger.debug('MainAppAdapter: Using legacy notification manager');
      return MainAppNotificationManager.legacy(ref);
    }

  } catch (e, stackTrace) {
    AppLogger.error(
      'MainAppAdapter: Manager routing failed',
      context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
    );

    // Emergency fallback to no-op manager
    return MainAppNotificationManager.noOp();
  }
}

/// Main Application Notification Settings
class MainAppNotificationSettings {
  final bool globallyEnabled;
  final bool prayerNotificationsEnabled;
  final bool communityNotificationsEnabled;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final Map<String, bool> prayerSettings;
  final String source; // 'unified', 'legacy', or 'safe'

  const MainAppNotificationSettings({
    required this.globallyEnabled,
    required this.prayerNotificationsEnabled,
    required this.communityNotificationsEnabled,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.prayerSettings,
    required this.source,
  });

  /// Create from unified notification settings
  factory MainAppNotificationSettings.fromUnified(UnifiedNotificationSettings unified) {
    return MainAppNotificationSettings(
      globallyEnabled: unified.globallyEnabled,
      prayerNotificationsEnabled: unified.prayerSettings.globallyEnabled,
      communityNotificationsEnabled: unified.communitySettings.globallyEnabled,
      soundEnabled: unified.soundSettings.enabled,
      vibrationEnabled: unified.vibrationSettings.enabled,
      prayerSettings: {
        'Fajr': unified.prayerSettings.fajrEnabled,
        'Dhuhr': unified.prayerSettings.dhuhrEnabled,
        'Asr': unified.prayerSettings.asrEnabled,
        'Maghrib': unified.prayerSettings.maghribEnabled,
        'Isha': unified.prayerSettings.ishaEnabled,
      },
      source: 'unified',
    );
  }

  /// Create legacy settings
  factory MainAppNotificationSettings.legacy() {
    return const MainAppNotificationSettings(
      globallyEnabled: true,
      prayerNotificationsEnabled: true,
      communityNotificationsEnabled: false,
      soundEnabled: true,
      vibrationEnabled: true,
      prayerSettings: {
        'Fajr': true,
        'Dhuhr': true,
        'Asr': true,
        'Maghrib': true,
        'Isha': true,
      },
      source: 'legacy',
    );
  }

  /// Create safe fallback settings
  factory MainAppNotificationSettings.safe() {
    return const MainAppNotificationSettings(
      globallyEnabled: false,
      prayerNotificationsEnabled: false,
      communityNotificationsEnabled: false,
      soundEnabled: false,
      vibrationEnabled: false,
      prayerSettings: {
        'Fajr': false,
        'Dhuhr': false,
        'Asr': false,
        'Maghrib': false,
        'Isha': false,
      },
      source: 'safe',
    );
  }

  /// Check if prayer notification is enabled for specific prayer
  bool isPrayerEnabled(String prayerName) {
    return globallyEnabled && 
           prayerNotificationsEnabled && 
           (prayerSettings[prayerName] ?? false);
  }
}

/// Main Application Notification Manager
class MainAppNotificationManager {
  final NotificationManagerInterface _manager;
  final String source;

  MainAppNotificationManager._(this._manager, this.source);

  /// Create from unified notification manager
  factory MainAppNotificationManager.fromUnified(NotificationManagerInterface unified) {
    return MainAppNotificationManager._(unified, 'unified');
  }

  /// Create legacy manager
  factory MainAppNotificationManager.legacy(Ref ref) {
    return MainAppNotificationManager._(LegacyNotificationManagerAdapter(ref), 'legacy');
  }

  /// Create no-op manager
  factory MainAppNotificationManager.noOp() {
    return MainAppNotificationManager._(NoOpNotificationManager(), 'noOp');
  }

  /// Schedule prayer notifications
  Future<void> schedulePrayerNotifications({
    required DateTime date,
    required double latitude,
    required double longitude,
  }) async {
    try {
      await _manager.schedulePrayerNotifications(
        date: date,
        latitude: latitude,
        longitude: longitude,
      );

      AppLogger.debug(
        'MainAppNotificationManager: Scheduled prayer notifications',
        context: {
          'source': source,
          'date': date.toIso8601String(),
          'latitude': latitude,
          'longitude': longitude,
        },
      );

    } catch (e, stackTrace) {
      AppLogger.error(
        'MainAppNotificationManager: Failed to schedule prayer notifications',
        context: {
          'source': source,
          'error': e.toString(),
          'stackTrace': stackTrace.toString(),
        },
      );
      rethrow;
    }
  }

  /// Cancel all notifications
  Future<void> cancelAllNotifications() async {
    try {
      await _manager.cancelAllNotifications();

      AppLogger.debug(
        'MainAppNotificationManager: Cancelled all notifications',
        context: {'source': source},
      );

    } catch (e, stackTrace) {
      AppLogger.error(
        'MainAppNotificationManager: Failed to cancel notifications',
        context: {
          'source': source,
          'error': e.toString(),
          'stackTrace': stackTrace.toString(),
        },
      );
      rethrow;
    }
  }

  /// Validate configuration
  Future<void> validateConfiguration() async {
    try {
      await _manager.validateConfiguration();

      AppLogger.debug(
        'MainAppNotificationManager: Configuration validated',
        context: {'source': source},
      );

    } catch (e, stackTrace) {
      AppLogger.error(
        'MainAppNotificationManager: Configuration validation failed',
        context: {
          'source': source,
          'error': e.toString(),
          'stackTrace': stackTrace.toString(),
        },
      );
      rethrow;
    }
  }

  /// Get pending notifications
  Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    try {
      final notifications = await _manager.getPendingNotifications();

      AppLogger.debug(
        'MainAppNotificationManager: Retrieved pending notifications',
        context: {
          'source': source,
          'count': notifications.length,
        },
      );

      return notifications;

    } catch (e, stackTrace) {
      AppLogger.error(
        'MainAppNotificationManager: Failed to get pending notifications',
        context: {
          'source': source,
          'error': e.toString(),
          'stackTrace': stackTrace.toString(),
        },
      );
      rethrow;
    }
  }
}

/// Main Application Adapter Status Provider
@riverpod
Future<MainAppAdapterStatus> mainAppAdapterStatus(MainAppAdapterStatusRef ref) async {
  try {
    final deploymentConfig = await ref.watch(deploymentConfigProvider.future);
    final settings = await ref.watch(mainAppNotificationSettingsProvider.future);
    
    return MainAppAdapterStatus(
      isActive: true,
      usingUnifiedProvider: deploymentConfig.shouldUseUnifiedProvider,
      settingsSource: settings.source,
      lastUpdate: DateTime.now(),
    );

  } catch (e, stackTrace) {
    AppLogger.error(
      'MainAppAdapter: Status check failed',
      context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
    );

    return MainAppAdapterStatus(
      isActive: false,
      usingUnifiedProvider: false,
      settingsSource: 'error',
      lastUpdate: DateTime.now(),
      error: e.toString(),
    );
  }
}

/// Main Application Adapter Status
class MainAppAdapterStatus {
  final bool isActive;
  final bool usingUnifiedProvider;
  final String settingsSource;
  final DateTime lastUpdate;
  final String? error;

  const MainAppAdapterStatus({
    required this.isActive,
    required this.usingUnifiedProvider,
    required this.settingsSource,
    required this.lastUpdate,
    this.error,
  });

  /// Get status summary
  String get statusSummary {
    if (error != null) return 'Error: $error';
    if (!isActive) return 'Inactive';
    if (usingUnifiedProvider) return 'Using Unified Provider';
    return 'Using Legacy Provider';
  }
}

// Placeholder types for compilation
class UnifiedNotificationSettings {
  final bool globallyEnabled;
  final PrayerSettingsGroup prayerSettings;
  final CommunitySettingsGroup communitySettings;
  final SoundSettingsGroup soundSettings;
  final VibrationSettingsGroup vibrationSettings;

  const UnifiedNotificationSettings({
    required this.globallyEnabled,
    required this.prayerSettings,
    required this.communitySettings,
    required this.soundSettings,
    required this.vibrationSettings,
  });
}

class PrayerSettingsGroup {
  final bool globallyEnabled;
  final bool fajrEnabled;
  final bool dhuhrEnabled;
  final bool asrEnabled;
  final bool maghribEnabled;
  final bool ishaEnabled;

  const PrayerSettingsGroup({
    required this.globallyEnabled,
    required this.fajrEnabled,
    required this.dhuhrEnabled,
    required this.asrEnabled,
    required this.maghribEnabled,
    required this.ishaEnabled,
  });
}

class CommunitySettingsGroup {
  final bool globallyEnabled;

  const CommunitySettingsGroup({required this.globallyEnabled});
}

class SoundSettingsGroup {
  final bool enabled;

  const SoundSettingsGroup({required this.enabled});
}

class VibrationSettingsGroup {
  final bool enabled;

  const VibrationSettingsGroup({required this.enabled});
}
