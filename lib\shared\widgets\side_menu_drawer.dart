import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter_islamic_icons/flutter_islamic_icons.dart';

import '../../core/providers/cache_refresh_provider.dart';
import '../../core/providers/app_settings_selectors.dart';
import '../../core/providers/app_settings_provider.dart';
import '../../core/settings/theme/theme_settings_provider.dart';
import '../../core/settings/compatibility/migration_feature_flags.dart';
import '../../core/theme/app_colors.dart';
import '../../core/theme/app_text_styles.dart';
import '../../core/theme/app_spacing.dart';
import '../../core/utils/language_helper.dart';
// Context7 MCP: Updated to use unified notification settings provider
import '../../core/notifications/providers/unified_notification_provider.dart';
import '../../features/notifications/domain/services/notification_manager.dart';
import '../../features/tutorial/domain/services/tutorial_service.dart';
import '../../main.dart';
import 'modern_toggle_switch.dart';

/// Side menu drawer for app navigation
/// Removes Home, Masjids, Duas, and Prayer Times (these are in bottom nav)
/// Keeps a single Login/Sign Up link and other settings options
class SideMenuDrawer extends ConsumerWidget {
  const SideMenuDrawer({super.key});

  /// Build a styled list tile with more compact design
  Widget _buildStyledListTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    Widget? trailing,
    VoidCallback? onTap,
    Color? iconColor,
  }) {
    // Check if the current language is Arabic
    final isArabic = Localizations.localeOf(context).languageCode == 'ar';

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 3.0),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(10),
        clipBehavior: Clip.antiAlias,
        child: InkWell(
          onTap: onTap,
          child: Ink(
            decoration: BoxDecoration(
              // Use blue gradient similar to masjid detail page
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [Colors.blue[50]!, Colors.blue[100]!],
              ),
              borderRadius: BorderRadius.circular(8), // Match detail page border radius
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(15), // Match detail page shadow
                  blurRadius: 3,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 10.0),
              child: Row(
                textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
                children: [
                  // Simple icon without background
                  Icon(icon, color: iconColor ?? Colors.blue[600]!, size: 20), // Match blue theme
                  const SizedBox(width: AppSpacing.sm + AppSpacing.xs),
                  Expanded(
                    child: Text(
                      title,
                      style: AppTextStyles.withColor(
                        AppTextStyles.body2.copyWith(fontWeight: FontWeight.w500),
                        AppColors.textPrimaryLight,
                      ),
                      textAlign: isArabic ? TextAlign.right : TextAlign.left,
                    ),
                  ),
                  if (trailing != null) trailing,
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build a simplified section title
  Widget _buildSectionTitle(String title) {
    return Consumer(
      builder: (context, ref, _) {
        // Check if the current language is Arabic - use select() for optimization
        final isArabic = ref.watch(currentLanguageCodeProvider) == 'ar';

        return Padding(
          padding: EdgeInsets.only(left: isArabic ? 14.0 : 16.0, right: isArabic ? 16.0 : 14.0, top: 12.0, bottom: 4.0),
          child: Row(
            textDirection: isArabic ? TextDirection.rtl : TextDirection.ltr,
            children: [
              Text(
                title,
                style: AppTextStyles.withColor(
                  AppTextStyles.overline.copyWith(fontWeight: FontWeight.bold),
                  AppColors.textSecondaryLight,
                ),
              ),
              const SizedBox(width: AppSpacing.sm),
              // Expanded line
              Expanded(
                child: Container(
                  height: 1,
                  color: AppColors.divider, // Use theme divider color
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Start tutorial by navigating to home page and triggering tutorial
  Future<void> _startTutorial(BuildContext context, WidgetRef ref) async {
    try {
      // Close the drawer first
      context.pop();

      // Use the tutorial service to handle navigation and tutorial display
      final tutorialService = ref.read(tutorialServiceProvider);
      await tutorialService.showTutorialFromSideMenu(context);
    } catch (e) {
      debugPrint('❌ TUTORIAL: Error starting tutorial from side menu: $e');

      // Show error message to user if context is still mounted
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('${context.l10n?.tutorialStartError ?? ''}: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get authentication status from the provider
    // Use watch to ensure the widget rebuilds when auth state changes
    final isAuthenticated = ref.watch(authProvider);

    // Check if there's a current Supabase session as a backup verification
    final hasSession = Supabase.instance.client.auth.currentSession != null;

    // Only consider authenticated if both the provider state and session are valid
    final isLoggedIn = isAuthenticated && hasSession;

    // Check if the current theme is dark
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Drawer(
      backgroundColor: isDarkMode ? AppColors.surfaceDark : AppColors.surfaceLight,
      child: Stack(
        children: [
          // Top background with simplified design
          Container(
            height: 80, // Significantly reduced for more compact design
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.blue[600]!, // Much darker blue for header
                  Colors.blue[700]!, // Even darker blue for gradient
                ], // Darker blue gradient for header
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(15), // Match detail page shadow
                  blurRadius: 3,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          ),

          // Main content with performance optimization
          RepaintBoundary(
            child: ListView(
              padding: EdgeInsets.zero,
              children: [
                // Compact header with horizontal layout
                SizedBox(
                  height: 80, // Reduced to match top background
                  child: Padding(
                    padding: const EdgeInsets.fromLTRB(16.0, 16.0, 16.0, 0.0),
                    child: Row(
                      children: [
                        // App icon with transparent background
                        Container(
                          width: 48,
                          height: 48,
                          decoration: const BoxDecoration(shape: BoxShape.circle, color: Colors.white),
                          child: Icon(
                            FlutterIslamicIcons.mosque,
                            size: 28,
                            color: Colors.blue[600]!, // Keep blue icon on white circle background
                          ),
                        ),
                        const SizedBox(width: AppSpacing.sm + AppSpacing.xs),
                        // App title with simplified styling
                        Text(
                          context.l10n?.appTitle ?? 'Masajid AlBahrain',
                          style: const TextStyle(
                            color: Colors.white, // Use white text on dark blue background
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: AppSpacing.sm), // Space after header

                const SizedBox(height: AppSpacing.sm + AppSpacing.xs),

                // hide settings line
                //              _buildSectionTitle(
                //                (context.l10n?.settingsTitle ?? 'SETTINGS').toUpperCase(),
                //              ),

                // Settings options
                // Full Map option
                _buildStyledListTile(
                  context: context,
                  icon: Icons.map,
                  title: context.l10n?.fullMap ?? 'Full Map',
                  onTap: () {
                    context.pop();
                    context.push('/full-map');
                  },
                ),

                // Favorites option
                _buildStyledListTile(
                  context: context,
                  icon: Icons.star,
                  title: context.l10n?.favoriteTitle ?? 'Favorites',
                  onTap: () {
                    context.pop();
                    context.push('/favorites');
                  },
                ),

                // Events option
                _buildStyledListTile(
                  context: context,
                  icon: Icons.event,
                  title: context.l10n?.eventsTitle ?? 'Events',
                  onTap: () {
                    context.pop();
                    context.push('/events');
                  },
                ),

                // About
                _buildStyledListTile(
                  context: context,
                  icon: Icons.info,
                  title: context.l10n?.aboutTitle ?? 'About',
                  onTap: () {
                    context.pop();
                    context.push('/about');
                  },
                ),

                // Contact Us
                _buildStyledListTile(
                  context: context,
                  icon: Icons.contact_support,
                  title: context.l10n?.contactTitle ?? 'Contact Us',
                  onTap: () {
                    context.pop();
                    context.push('/contact');
                  },
                ),

                // Tutorial
                _buildStyledListTile(
                  context: context,
                  icon: Icons.help_outline,
                  title: context.l10n?.tutorialTitle ?? 'Tutorial',
                  onTap: () {
                    _startTutorial(context, ref);
                  },
                ),

                // Settings option hidden as requested
                // _buildStyledListTile(
                //   context: context,
                //   icon: Icons.settings,
                //   title: context.l10n?.settingsTitle ?? 'Settings',
                //   onTap: () {
                //     context.pop();
                //     // context.push('/settings');
                //   },
                // ),

                //  one more settings line
                const SizedBox(height: AppSpacing.sm + AppSpacing.xs),

                _buildSectionTitle((context.l10n?.settingsTitle ?? 'SETTINGS').toUpperCase()),
                // end of one more

                // Notifications settings with toggle
                _buildStyledListTile(
                  context: context,
                  icon: Icons.notifications,
                  title: context.l10n?.notificationsTitle ?? 'Notifications',
                  trailing: Consumer(
                    builder: (context, ref, _) {
                      // Use select() to only rebuild when enabled status changes
                      final isEnabled = ref.watch(
                        notificationSettingsNotifierProvider.select((settings) => settings.notificationsEnabled),
                      );
                      return ModernToggleSwitch(
                        isActive: isEnabled,
                        onToggle: (value) {
                          ref.read(notificationSettingsNotifierProvider.notifier).toggleNotificationsEnabled().then((
                            _,
                          ) {
                            // Schedule or cancel notifications based on new setting
                            if (value) {
                              ref.read(notificationManagerProvider).schedulePrayerTimeNotifications();
                            } else {
                              ref.read(notificationManagerProvider).cancelAllNotifications();
                            }
                          });
                        },
                        width: 56.0,
                        height: 28.0,
                        activeColor: Colors.blue[600]!, // Match blue theme
                        inactiveColor: AppColors.inactive,
                        activeThumbColor: AppColors.textOnPrimary,
                        inactiveThumbColor: AppColors.textOnPrimary,
                      );
                    },
                  ),
                  onTap: () {
                    context.pop();
                    context.push('/notifications');
                  },
                ),

                // Theme toggle
                _buildStyledListTile(
                  context: context,
                  icon: Icons.dark_mode,
                  title: context.l10n?.themeTitle ?? 'Theme',
                  trailing: ModernToggleSwitch(
                    // isActive is true when theme is dark - use select() for optimization
                    isActive: ref.watch(currentThemeProvider) == ThemeMode.dark,
                    onToggle: (isActive) async {
                      // Toggle between light and dark theme
                      final newThemeMode = isActive ? ThemeMode.dark : ThemeMode.light;

                      // Check migration feature flags following Context7 MCP best practices
                      final migrationFlags = ref.read(migrationFeatureFlagsNotifierProvider);
                      final useThemeProvider = migrationFlags.when(
                        data: (flags) => flags.useThemeProvider,
                        loading: () => false, // Fallback to legacy during loading
                        error: (_, __) => false, // Fallback to legacy on error
                      );

                      if (useThemeProvider) {
                        // Use new domain-specific theme provider
                        await ref.read(themeSettingsProvider.notifier).updateThemeMode(newThemeMode);
                      } else {
                        // Fallback to legacy monolithic provider
                        await ref.read(appSettingsProvider.notifier).updateTheme(newThemeMode);
                      }
                    },
                    width: 56.0,
                    height: 28.0,
                    activeColor: Colors.blue[600]!, // Match blue theme
                    inactiveColor: AppColors.inactive,
                    leftLabel: '🌞',
                    rightLabel: '🌙',
                    activeThumbColor: AppColors.textOnPrimary,
                    inactiveThumbColor: AppColors.textOnPrimary,
                  ),
                  onTap: null, // Disable tap on the entire tile, only toggle works
                ),

                // Language toggle
                _buildStyledListTile(
                  context: context,
                  icon: Icons.language,
                  title: context.l10n?.languageTitle ?? 'Language',
                  trailing: ModernToggleSwitch(
                    // isActive is true when language is Arabic - use select() for optimization
                    isActive: ref.watch(currentLanguageCodeProvider) == 'ar',
                    onToggle: (isActive) {
                      // Use LanguageHelper to safely change the language
                      final newLanguage = isActive ? 'ar' : 'en';
                      LanguageHelper.changeLanguage(ref, newLanguage);
                    },
                    width: 56.0,
                    height: 28.0,
                    activeColor: Colors.blue[600]!, // Match blue theme
                    inactiveColor: AppColors.inactive,
                    leftLabel: context.l10n?.englishLabel ?? '',
                    rightLabel: context.l10n?.arabicLabel ?? '',
                    activeThumbColor: AppColors.textOnPrimary,
                    inactiveThumbColor: AppColors.textOnPrimary,
                  ),
                  onTap: null, // Disable tap on the entire tile, only toggle works
                ),

                const SizedBox(height: AppSpacing.md),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
