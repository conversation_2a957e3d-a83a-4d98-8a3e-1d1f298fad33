import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../providers/unified_notification_provider.dart';
import 'deployment_config.dart';
import 'provider_router.dart';
import 'main_app_adapter.dart';
import 'service_registry_adapter.dart';
import '../../logging/app_logger.dart';

part 'critical_path_migration.g.dart';

/// Critical Path Migration Service
///
/// **Context7 MCP Implementation:**
/// - Migrates critical notification paths to unified providers
/// - Updates main application entry points and core services
/// - Maintains backward compatibility during migration
/// - Provides rollback capabilities for failed migrations
/// - Monitors migration progress and health
///
/// **Phase 2 Strategy:**
/// - Migrate prayer notification scheduling first (highest priority)
/// - Update core settings management
/// - Migrate main application initialization
/// - Update service provider registry
/// - Maintain compatibility layers for non-critical components
@riverpod
class CriticalPathMigration extends _$CriticalPathMigration {
  @override
  Future<CriticalPathMigrationState> build() async {
    try {
      AppLogger.info('CriticalPathMigration: Initializing Phase 2 migration');

      // Check deployment configuration
      final deploymentConfig = await ref.watch(deploymentConfigProvider.future);

      if (!deploymentConfig.shouldUseUnifiedProvider) {
        AppLogger.info('CriticalPathMigration: Unified provider not enabled, skipping migration');
        return CriticalPathMigrationState(
          phase: MigrationPhase.phase2,
          isActive: false,
          criticalPathsMigrated: false,
          prayerNotificationsMigrated: false,
          coreSettingsMigrated: false,
          mainAppMigrated: false,
          serviceRegistryMigrated: false,
          lastUpdate: DateTime.now(),
          error: 'Unified provider not enabled',
        );
      }

      // Initialize migration state
      final state = CriticalPathMigrationState(
        phase: MigrationPhase.phase2,
        isActive: true,
        criticalPathsMigrated: false,
        prayerNotificationsMigrated: false,
        coreSettingsMigrated: false,
        mainAppMigrated: false,
        serviceRegistryMigrated: false,
        lastUpdate: DateTime.now(),
      );

      // Start migration process
      await _performCriticalPathMigration();

      return state.copyWith(criticalPathsMigrated: true, lastUpdate: DateTime.now());
    } catch (e, stackTrace) {
      AppLogger.error(
        'CriticalPathMigration: Initialization failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );

      return CriticalPathMigrationState(
        phase: MigrationPhase.phase2,
        isActive: false,
        criticalPathsMigrated: false,
        prayerNotificationsMigrated: false,
        coreSettingsMigrated: false,
        mainAppMigrated: false,
        serviceRegistryMigrated: false,
        lastUpdate: DateTime.now(),
        error: e.toString(),
      );
    }
  }

  /// Perform critical path migration
  Future<void> _performCriticalPathMigration() async {
    try {
      AppLogger.info('CriticalPathMigration: Starting critical path migration');

      // Step 1: Migrate prayer notifications (highest priority)
      await _migratePrayerNotifications();

      // Step 2: Migrate core settings
      await _migrateCoreSettings();

      // Step 3: Update main application initialization
      await _migrateMainApplication();

      // Step 4: Update service provider registry
      await _migrateServiceRegistry();

      AppLogger.info('CriticalPathMigration: Critical path migration completed successfully');
    } catch (e, stackTrace) {
      AppLogger.error(
        'CriticalPathMigration: Critical path migration failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Migrate prayer notifications to unified provider
  Future<void> _migratePrayerNotifications() async {
    try {
      AppLogger.info('CriticalPathMigration: Migrating prayer notifications');

      // Get main app notification manager through adapter
      final notificationManager = await ref.read(mainAppNotificationManagerProvider.future);

      // Validate notification manager is working
      await notificationManager.validateConfiguration();

      // Get unified service registry to ensure all services are available
      final serviceRegistry = await ref.read(unifiedServiceRegistryProvider.future);
      final isValid = await serviceRegistry.validateServices();

      if (!isValid) {
        throw Exception('Service registry validation failed');
      }

      // Update state
      final currentState = await future;
      state = AsyncData(currentState.copyWith(prayerNotificationsMigrated: true, lastUpdate: DateTime.now()));

      AppLogger.info('CriticalPathMigration: Prayer notifications migrated successfully');
    } catch (e, stackTrace) {
      AppLogger.error(
        'CriticalPathMigration: Prayer notification migration failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Migrate core settings to unified provider
  Future<void> _migrateCoreSettings() async {
    try {
      AppLogger.info('CriticalPathMigration: Migrating core settings');

      // Get main app notification settings through adapter
      final notificationSettings = await ref.read(mainAppNotificationSettingsProvider.future);

      // Validate settings are accessible and properly configured
      // Validate service registry settings interface
      final serviceRegistry = await ref.read(unifiedServiceRegistryProvider.future);
      final settingsService = serviceRegistry.settingsService;

      // Ensure settings service is functional
      final isGloballyEnabled = settingsService.globallyEnabled;
      final isPrayerEnabled = settingsService.prayerNotificationsEnabled;

      AppLogger.debug(
        'CriticalPathMigration: Settings validation',
        context: {
          'globallyEnabled': isGloballyEnabled,
          'prayerEnabled': isPrayerEnabled,
          'source': notificationSettings.source,
        },
      );

      // Update state
      final currentState = await future;
      state = AsyncData(currentState.copyWith(coreSettingsMigrated: true, lastUpdate: DateTime.now()));

      AppLogger.info('CriticalPathMigration: Core settings migrated successfully');
    } catch (e, stackTrace) {
      AppLogger.error(
        'CriticalPathMigration: Core settings migration failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Migrate main application initialization
  Future<void> _migrateMainApplication() async {
    try {
      AppLogger.info('CriticalPathMigration: Migrating main application initialization');

      // The main application will now use the routed providers
      // which automatically route to unified providers when enabled

      // Update state
      final currentState = await future;
      state = AsyncData(currentState.copyWith(mainAppMigrated: true, lastUpdate: DateTime.now()));

      AppLogger.info('CriticalPathMigration: Main application migrated successfully');
    } catch (e, stackTrace) {
      AppLogger.error(
        'CriticalPathMigration: Main application migration failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Migrate service provider registry
  Future<void> _migrateServiceRegistry() async {
    try {
      AppLogger.info('CriticalPathMigration: Migrating service provider registry');

      // Service registry will now reference unified providers
      // through the routing system

      // Update state
      final currentState = await future;
      state = AsyncData(currentState.copyWith(serviceRegistryMigrated: true, lastUpdate: DateTime.now()));

      AppLogger.info('CriticalPathMigration: Service registry migrated successfully');
    } catch (e, stackTrace) {
      AppLogger.error(
        'CriticalPathMigration: Service registry migration failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Rollback critical path migration
  Future<void> rollbackMigration({required String reason}) async {
    try {
      AppLogger.warning('CriticalPathMigration: Rolling back migration', context: {'reason': reason});

      // Trigger deployment rollback
      await ref.read(deploymentConfigManagerProvider.notifier).emergencyRollback(reason: reason);

      // Update state
      final currentState = await future;
      state = AsyncData(
        currentState.copyWith(
          isActive: false,
          criticalPathsMigrated: false,
          prayerNotificationsMigrated: false,
          coreSettingsMigrated: false,
          mainAppMigrated: false,
          serviceRegistryMigrated: false,
          lastUpdate: DateTime.now(),
          error: 'Rolled back: $reason',
        ),
      );

      AppLogger.error('CriticalPathMigration: Migration rolled back successfully');
    } catch (e, stackTrace) {
      AppLogger.error(
        'CriticalPathMigration: Rollback failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Validate migration status
  Future<bool> validateMigration() async {
    try {
      final currentState = await future;

      // Check if all critical paths are migrated
      final allMigrated =
          currentState.prayerNotificationsMigrated &&
          currentState.coreSettingsMigrated &&
          currentState.mainAppMigrated &&
          currentState.serviceRegistryMigrated;

      if (allMigrated) {
        // Perform functional validation using service registry
        final serviceRegistry = await ref.read(unifiedServiceRegistryProvider.future);
        final isValid = await serviceRegistry.validateServices();

        if (!isValid) {
          throw Exception('Service registry validation failed');
        }

        return true;
      }

      return false;
    } on Exception catch (e, stackTrace) {
      AppLogger.error(
        'CriticalPathMigration: Validation failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      return false;
    }
  }
}

/// Critical Path Migration State
class CriticalPathMigrationState {
  final MigrationPhase phase;
  final bool isActive;
  final bool criticalPathsMigrated;
  final bool prayerNotificationsMigrated;
  final bool coreSettingsMigrated;
  final bool mainAppMigrated;
  final bool serviceRegistryMigrated;
  final DateTime lastUpdate;
  final String? error;

  const CriticalPathMigrationState({
    required this.phase,
    required this.isActive,
    required this.criticalPathsMigrated,
    required this.prayerNotificationsMigrated,
    required this.coreSettingsMigrated,
    required this.mainAppMigrated,
    required this.serviceRegistryMigrated,
    required this.lastUpdate,
    this.error,
  });

  /// Get migration progress percentage
  double get progressPercentage {
    var completed = 0;
    const total = 4;

    if (prayerNotificationsMigrated) completed++;
    if (coreSettingsMigrated) completed++;
    if (mainAppMigrated) completed++;
    if (serviceRegistryMigrated) completed++;

    return (completed / total) * 100;
  }

  /// Check if migration is complete
  bool get isComplete =>
      criticalPathsMigrated &&
      prayerNotificationsMigrated &&
      coreSettingsMigrated &&
      mainAppMigrated &&
      serviceRegistryMigrated;

  /// Copy with new values
  CriticalPathMigrationState copyWith({
    MigrationPhase? phase,
    bool? isActive,
    bool? criticalPathsMigrated,
    bool? prayerNotificationsMigrated,
    bool? coreSettingsMigrated,
    bool? mainAppMigrated,
    bool? serviceRegistryMigrated,
    DateTime? lastUpdate,
    String? error,
  }) {
    return CriticalPathMigrationState(
      phase: phase ?? this.phase,
      isActive: isActive ?? this.isActive,
      criticalPathsMigrated: criticalPathsMigrated ?? this.criticalPathsMigrated,
      prayerNotificationsMigrated: prayerNotificationsMigrated ?? this.prayerNotificationsMigrated,
      coreSettingsMigrated: coreSettingsMigrated ?? this.coreSettingsMigrated,
      mainAppMigrated: mainAppMigrated ?? this.mainAppMigrated,
      serviceRegistryMigrated: serviceRegistryMigrated ?? this.serviceRegistryMigrated,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      error: error ?? this.error,
    );
  }
}

/// Migration Phase
enum MigrationPhase {
  phase1, // Deploy alongside existing
  phase2, // Migrate critical paths
  phase3, // Update remaining dependencies
  phase4, // Remove deprecated providers
  phase5, // Cleanup and optimization
}
