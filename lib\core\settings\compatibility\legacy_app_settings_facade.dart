import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../features/notifications/domain/entities/notification_settings.dart' as legacy;
import '../../../features/prayer_times/domain/enums/prayer_enum.dart';
import '../../providers/app_settings_state.dart';
import '../../utils/app_logger.dart';
import '../advanced/advanced_settings_provider.dart';
import '../advanced/advanced_settings_state.dart';
import '../location/location_settings_provider.dart';
import '../location/location_settings_state.dart';
// Context7 MCP: Updated to use unified notification settings provider
import '../../notifications/providers/unified_notification_provider.dart' as unified;
import '../notification/notification_settings_state.dart';
import '../performance/performance_settings_provider.dart';
import '../performance/performance_settings_state.dart';
import '../prayer_times/prayer_times_settings_provider.dart';
import '../prayer_times/prayer_times_settings_state.dart';
import '../theme/theme_settings_provider.dart';
import '../theme/theme_settings_state.dart';
import 'migration_feature_flags.dart';

part 'legacy_app_settings_facade.g.dart';

/// Legacy App Settings Facade following Context7 MCP best practices
///
/// **⚠️ TRANSITIONAL COMPATIBILITY LAYER - Use for migration only**
///
/// This facade provides backward compatibility for existing code that depends
/// on the monolithic AppSettingsProvider while internally using the new
/// domain-specific providers. This enables gradual migration without breaking
/// existing functionality.
///
/// **🚨 DEPRECATION NOTICE:**
/// - This is a temporary compatibility layer for migration purposes
/// - Will be removed in v2.0.0 along with AppSettingsProvider
/// - Use domain-specific providers for new code
/// - Migrate existing code gradually using feature flags
///
/// **Key Features:**
/// - ✅ **Backward Compatibility**: Maintains exact same API as original AppSettingsProvider
/// - ✅ **Domain Provider Integration**: Internally uses new domain-specific providers
/// - ✅ **Feature Flag Support**: Allows gradual rollout with fallback mechanisms
/// - ✅ **Migration Safety**: Ensures no functionality loss during transition
/// - ✅ **Performance Optimization**: Leverages domain provider performance benefits
/// - ✅ **Error Handling**: Robust error recovery with fallback to legacy system
/// - ⚠️ **Deprecation Warnings**: Logs warnings to guide migration
///
/// **Migration Timeline:**
/// - v1.8.0: Legacy facade available for compatibility (current)
/// - v1.9.0: Deprecation warnings added to guide migration
/// - v2.0.0: Complete removal of legacy facade and AppSettingsProvider
///
/// **Usage (Temporary):**
/// ```dart
/// // Existing code continues to work unchanged (with warnings)
/// final settingsAsync = ref.watch(legacyAppSettingsProvider);
/// await ref.read(legacyAppSettingsProvider.notifier).updateTheme(ThemeMode.dark);
/// ```
///
/// **Recommended Migration:**
/// ```dart
/// // New code should use domain-specific providers
/// final themeAsync = ref.watch(themeSettingsProvider);
/// await ref.read(themeSettingsProvider.notifier).updateThemeMode(ThemeMode.dark);
/// ```
///
/// **Migration Strategy:**
/// 1. Replace AppSettingsProvider with LegacyAppSettingsProvider (temporary)
/// 2. Enable feature flags gradually for each domain
/// 3. Update code to use domain-specific providers
/// 4. Remove legacy facade once migration is complete
@Deprecated(
  'LegacyAppSettings is a temporary compatibility layer. '
  'Use domain-specific providers instead: themeSettingsProvider, '
  'notificationSettingsProvider, prayerTimesSettingsProvider, etc. '
  'Will be removed in v2.0.0. See migration guide for details.',
)
@riverpod
class LegacyAppSettings extends _$LegacyAppSettings {
  /// Initialize legacy app settings facade
  ///
  /// **⚠️ DEPRECATED: This is a temporary compatibility layer**
  ///
  /// This method combines data from all domain providers to create a unified
  /// AppSettingsState that matches the original monolithic structure.
  @override
  Future<AppSettingsState> build() async {
    try {
      // Log deprecation warning to guide migration
      AppLogger.warning(
        '⚠️ DEPRECATION WARNING: LegacyAppSettings is deprecated and will be removed in v2.0.0. '
        'Please migrate to domain-specific providers: themeSettingsProvider, '
        'notificationSettingsProvider, prayerTimesSettingsProvider, etc. '
        'See migration guide: docs/migration/app-settings-service-deprecation.md',
      );

      AppLogger.debug('LegacyAppSettings: Initializing compatibility facade');

      // Check feature flags to determine which providers to use
      final featureFlags = await ref.read(migrationFeatureFlagsNotifierProvider.future);

      // Load data from domain providers or fallback to legacy
      final themeData = await _loadThemeData(featureFlags);
      final notificationData = await _loadNotificationData(featureFlags);
      final prayerTimesData = await _loadPrayerTimesData(featureFlags);
      final locationData = await _loadLocationData(featureFlags);
      final performanceData = await _loadPerformanceData(featureFlags);
      final advancedData = await _loadAdvancedData(featureFlags);

      // Combine all domain data into unified AppSettingsState
      final unifiedState = AppSettingsState(
        // Theme & Appearance
        themeMode: themeData.themeMode,
        localeCode: themeData.languageCode, // Use languageCode for localeCode
        languageCode: themeData.languageCode,
        fontScale: themeData.fontScale,
        highContrastMode: themeData.highContrastMode,

        // Notifications
        notificationsEnabled: notificationData.notificationsEnabled,
        notificationSound: _convertNotificationSound(notificationData.globalSound),
        useVibration: notificationData.useVibration,
        notificationTiming: _convertNotificationTiming(notificationData.globalTiming),
        notificationMinutesBefore: notificationData.globalTiming.minutesBefore,
        prayerNotificationsEnabled: _convertPrayerNotifications(notificationData),

        // Prayer Times
        calculationMethod: prayerTimesData.calculationMethod.name,
        prayerTimeAdjustments: _convertPrayerAdjustments(prayerTimesData),
        prayerTimeVisibility: _convertPrayerVisibility(prayerTimesData),
        autoRefreshPrayerTimes: prayerTimesData.autoRefreshEnabled,
        prayerTimesCacheHours: prayerTimesData.cacheExpirationHours,

        // Location & GPS
        useCurrentLocation: locationData.useCurrentLocation,
        locationAccuracy: _convertLocationAccuracy(locationData.locationAccuracy),
        locationTolerance: locationData.locationTolerance,
        savedLatitude: locationData.savedLatitude ?? 26.174271,
        savedLongitude: locationData.savedLongitude ?? 50.287766,
        savedLocationName: locationData.savedLocationName ?? 'Bahrain',

        // Performance
        performanceMode: performanceData.performanceMode.name,
        offlineMode: performanceData.offlineMode,
        backgroundRefreshEnabled: performanceData.backgroundRefreshEnabled,
        cacheExpirationHours: performanceData.cacheExpirationHours,

        // Privacy & Security (default to enabled unless in production)
        analyticsEnabled: advancedData.environment != AdvancedEnvironment.production,
        crashReportingEnabled: advancedData.environment != AdvancedEnvironment.production,
        usageStatsEnabled: advancedData.environment != AdvancedEnvironment.production,

        // Advanced
        debugModeEnabled: advancedData.debugModeEnabled,
        developerOptionsEnabled: advancedData.enabledDeveloperTools.isNotEmpty,
        experimentalFeaturesEnabled: advancedData.environment != AdvancedEnvironment.production,

        // Metadata
        settingsVersion: 2, // Indicate this is from new domain providers
        lastUpdated: DateTime.now().toIso8601String(),
      );

      AppLogger.debug('LegacyAppSettings: Facade initialized successfully');
      return unifiedState;
    } on Exception catch (e, stackTrace) {
      AppLogger.error('LegacyAppSettings: Error initializing facade', e, stackTrace);

      // Fallback to default state on error
      return const AppSettingsState();
    }
  }

  // ==================== THEME & APPEARANCE METHODS ====================

  /// Update the app theme mode
  ///
  /// Routes to appropriate provider based on feature flags
  Future<void> updateTheme(ThemeMode themeMode) async {
    try {
      final featureFlags = await ref.read(migrationFeatureFlagsNotifierProvider.future);

      if (featureFlags.useThemeProvider) {
        // Use new theme provider
        await ref.read(themeSettingsProvider.notifier).updateThemeMode(themeMode);
      } else {
        // Fallback to legacy implementation
        await _updateLegacyTheme(themeMode);
      }

      // Trigger facade rebuild
      ref.invalidateSelf();
    } on Exception catch (e, stackTrace) {
      AppLogger.error('LegacyAppSettings: Error updating theme', e, stackTrace);
      rethrow;
    }
  }

  /// Update language code
  ///
  /// Routes to appropriate provider based on feature flags
  Future<void> updateLanguage(String languageCode) async {
    try {
      final featureFlags = await ref.read(migrationFeatureFlagsNotifierProvider.future);

      if (featureFlags.useThemeProvider) {
        // Use new theme provider
        await ref.read(themeSettingsProvider.notifier).updateLanguage(languageCode);
      } else {
        // Fallback to legacy implementation
        await _updateLegacyLanguage(languageCode);
      }

      // Trigger facade rebuild
      ref.invalidateSelf();
    } on Exception catch (e, stackTrace) {
      AppLogger.error('LegacyAppSettings: Error updating language', e, stackTrace);
      rethrow;
    }
  }

  // ==================== NOTIFICATION METHODS ====================

  /// Toggle notifications enabled/disabled
  ///
  /// Routes to appropriate provider based on feature flags
  Future<void> toggleNotifications(bool enabled) async {
    try {
      final featureFlags = await ref.read(migrationFeatureFlagsNotifierProvider.future);

      if (featureFlags.useNotificationProvider) {
        // Use new notification provider
        // Context7 MCP: TODO - Implement general settings update in unified provider
        // For now, fallback to legacy implementation during migration
        await _updateLegacyNotifications(enabled);
      } else {
        // Fallback to legacy implementation
        await _updateLegacyNotifications(enabled);
      }

      // Trigger facade rebuild
      ref.invalidateSelf();
    } on Exception catch (e, stackTrace) {
      AppLogger.error('LegacyAppSettings: Error toggling notifications', e, stackTrace);
      rethrow;
    }
  }

  // ==================== PRAYER TIMES METHODS ====================

  /// Update prayer calculation method
  ///
  /// Routes to appropriate provider based on feature flags
  Future<void> updateCalculationMethod(String method) async {
    try {
      final featureFlags = await ref.read(migrationFeatureFlagsNotifierProvider.future);

      if (featureFlags.usePrayerTimesProvider) {
        // Use new prayer times provider
        final calculationMethod = _parseCalculationMethod(method);
        await ref.read(prayerTimesSettingsProvider.notifier).updateCalculationMethod(calculationMethod);
      } else {
        // Fallback to legacy implementation
        await _updateLegacyCalculationMethod(method);
      }

      // Trigger facade rebuild
      ref.invalidateSelf();
    } on Exception catch (e, stackTrace) {
      AppLogger.error('LegacyAppSettings: Error updating calculation method', e, stackTrace);
      rethrow;
    }
  }

  // ==================== LOCATION METHODS ====================

  /// Update GPS usage preference
  ///
  /// Routes to appropriate provider based on feature flags
  Future<void> updateUseCurrentLocation(bool useGPS) async {
    try {
      final featureFlags = await ref.read(migrationFeatureFlagsNotifierProvider.future);

      if (featureFlags.useLocationProvider) {
        // Use new location provider
        await ref.read(locationSettingsProvider.notifier).updateUseCurrentLocation(useGPS);
      } else {
        // Fallback to legacy implementation
        await _updateLegacyUseCurrentLocation(useGPS);
      }

      // Trigger facade rebuild
      ref.invalidateSelf();
    } on Exception catch (e, stackTrace) {
      AppLogger.error('LegacyAppSettings: Error updating GPS usage', e, stackTrace);
      rethrow;
    }
  }

  // ==================== PERFORMANCE METHODS ====================

  /// Update performance mode
  ///
  /// Routes to appropriate provider based on feature flags
  Future<void> updatePerformanceMode(String mode) async {
    try {
      final featureFlags = await ref.read(migrationFeatureFlagsNotifierProvider.future);

      if (featureFlags.usePerformanceProvider) {
        // Use new performance provider
        final performanceMode = _parsePerformanceMode(mode);
        await ref.read(performanceSettingsProvider.notifier).setPerformanceMode(performanceMode);
      } else {
        // Fallback to legacy implementation
        await _updateLegacyPerformanceMode(mode);
      }

      // Trigger facade rebuild
      ref.invalidateSelf();
    } on Exception catch (e, stackTrace) {
      AppLogger.error('LegacyAppSettings: Error updating performance mode', e, stackTrace);
      rethrow;
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  /// Load theme data from appropriate provider
  Future<ThemeSettingsState> _loadThemeData(MigrationFeatureFlags featureFlags) async {
    if (featureFlags.useThemeProvider) {
      return await ref.read(themeSettingsProvider.future);
    } else {
      // Return default theme data for legacy fallback
      return _getDefaultThemeData();
    }
  }

  /// Load notification data from appropriate provider
  Future<NotificationSettingsState> _loadNotificationData(MigrationFeatureFlags featureFlags) async {
    if (featureFlags.useNotificationProvider) {
      // Context7 MCP: Use unified notification settings
      final unifiedSettings = await ref.read(unified.unifiedNotificationSettingsProvider.future);
      // Convert unified settings to legacy format for compatibility
      return _convertUnifiedToLegacyNotificationSettings(unifiedSettings);
    } else {
      // Return default notification data for legacy fallback
      return _getDefaultNotificationData();
    }
  }

  /// Load prayer times data from appropriate provider
  Future<PrayerTimesSettingsState> _loadPrayerTimesData(MigrationFeatureFlags featureFlags) async {
    if (featureFlags.usePrayerTimesProvider) {
      return await ref.read(prayerTimesSettingsProvider.future);
    } else {
      // Return default prayer times data for legacy fallback
      return _getDefaultPrayerTimesData();
    }
  }

  /// Load location data from appropriate provider
  Future<LocationSettingsState> _loadLocationData(MigrationFeatureFlags featureFlags) async {
    if (featureFlags.useLocationProvider) {
      return await ref.read(locationSettingsProvider.future);
    } else {
      // Return default location data for legacy fallback
      return _getDefaultLocationData();
    }
  }

  /// Load performance data from appropriate provider
  Future<PerformanceSettingsState> _loadPerformanceData(MigrationFeatureFlags featureFlags) async {
    if (featureFlags.usePerformanceProvider) {
      return await ref.read(performanceSettingsProvider.future);
    } else {
      // Return default performance data for legacy fallback
      return _getDefaultPerformanceData();
    }
  }

  /// Load advanced data from appropriate provider
  Future<AdvancedSettingsState> _loadAdvancedData(MigrationFeatureFlags featureFlags) async {
    if (featureFlags.useAdvancedProvider) {
      return await ref.read(advancedSettingsProvider.future);
    } else {
      // Return default advanced data for legacy fallback
      return _getDefaultAdvancedData();
    }
  }

  // ==================== DATA CONVERSION METHODS ====================

  /// Convert prayer notifications from new format to legacy format
  Map<String, bool> _convertPrayerNotifications(NotificationSettingsState notificationData) {
    try {
      final prayerNotifications = <String, bool>{};

      // Convert from new PrayerType enum to legacy string keys
      for (final entry in notificationData.prayerNotifications.entries) {
        prayerNotifications[entry.key.name] = entry.value.enabled;
      }

      return prayerNotifications;
    } on Exception catch (e) {
      AppLogger.warning('Error converting prayer notifications: $e');
      return {'fajr': true, 'sunrise': false, 'dhuhr': true, 'asr': true, 'maghrib': true, 'isha': true};
    }
  }

  /// Convert prayer adjustments from new format to legacy format
  Map<String, int> _convertPrayerAdjustments(PrayerTimesSettingsState prayerTimesData) {
    try {
      final adjustments = <String, int>{};

      // Convert from new PrayerType enum to legacy string keys
      for (final entry in prayerTimesData.prayerAdjustments.entries) {
        adjustments[entry.key.name] = entry.value;
      }

      return adjustments;
    } on Exception catch (e) {
      AppLogger.warning('Error converting prayer adjustments: $e');
      return {'fajr': 0, 'sunrise': 0, 'dhuhr': 0, 'asr': 0, 'maghrib': 0, 'isha': 0};
    }
  }

  /// Convert prayer visibility from new format to legacy format
  Map<String, bool> _convertPrayerVisibility(PrayerTimesSettingsState prayerTimesData) {
    try {
      final visibility = <String, bool>{};

      // Convert from new Prayer enum Set to legacy string keys
      for (final prayer in prayerTimesData.effectiveVisiblePrayers) {
        visibility[prayer.name] = true;
      }

      // Set false for prayers not in the visible set
      for (final prayer in Prayer.values) {
        if (!prayerTimesData.effectiveVisiblePrayers.contains(prayer)) {
          visibility[prayer.name] = false;
        }
      }

      return visibility;
    } on Exception catch (e) {
      AppLogger.warning('Error converting prayer visibility: $e');
      return {'fajr': true, 'sunrise': true, 'dhuhr': true, 'asr': true, 'maghrib': true, 'isha': true};
    }
  }

  /// Convert location accuracy from new format to legacy format
  LocationAccuracy _convertLocationAccuracy(dynamic newAccuracy) {
    try {
      // Map from new geolocator.LocationAccuracy to legacy LocationAccuracy
      switch (newAccuracy.toString()) {
        case 'LocationAccuracy.lowest':
          return LocationAccuracy.lowest;
        case 'LocationAccuracy.low':
          return LocationAccuracy.low;
        case 'LocationAccuracy.medium':
          return LocationAccuracy.medium;
        case 'LocationAccuracy.high':
          return LocationAccuracy.high;
        case 'LocationAccuracy.best':
          return LocationAccuracy.best;
        case 'LocationAccuracy.bestForNavigation':
          return LocationAccuracy.bestForNavigation;
        default:
          return LocationAccuracy.high;
      }
    } on Exception catch (e) {
      AppLogger.warning('Error converting location accuracy: $e');
      return LocationAccuracy.high;
    }
  }

  // ==================== LEGACY FALLBACK METHODS ====================

  /// Update theme using legacy implementation
  Future<void> _updateLegacyTheme(ThemeMode themeMode) async {
    // Implementation would call original AppSettingsProvider
    // This is a placeholder for legacy fallback
    AppLogger.debug('Using legacy theme update for: ${themeMode.name}');
  }

  /// Update language using legacy implementation
  Future<void> _updateLegacyLanguage(String languageCode) async {
    // Implementation would call original AppSettingsProvider
    // This is a placeholder for legacy fallback
    AppLogger.debug('Using legacy language update for: $languageCode');
  }

  /// Update notifications using legacy implementation
  Future<void> _updateLegacyNotifications(bool enabled) async {
    // Implementation would call original AppSettingsProvider
    // This is a placeholder for legacy fallback
    AppLogger.debug('Using legacy notifications update for: $enabled');
  }

  /// Update calculation method using legacy implementation
  Future<void> _updateLegacyCalculationMethod(String method) async {
    // Implementation would call original AppSettingsProvider
    // This is a placeholder for legacy fallback
    AppLogger.debug('Using legacy calculation method update for: $method');
  }

  /// Update GPS usage using legacy implementation
  Future<void> _updateLegacyUseCurrentLocation(bool useGPS) async {
    // Implementation would call original AppSettingsProvider
    // This is a placeholder for legacy fallback
    AppLogger.debug('Using legacy GPS usage update for: $useGPS');
  }

  /// Update performance mode using legacy implementation
  Future<void> _updateLegacyPerformanceMode(String mode) async {
    // Implementation would call original AppSettingsProvider
    // This is a placeholder for legacy fallback
    AppLogger.debug('Using legacy performance mode update for: $mode');
  }

  // ==================== PARSING METHODS ====================

  /// Parse calculation method string to enum
  dynamic _parseCalculationMethod(String method) {
    // This would parse the string to the appropriate enum
    // Placeholder implementation
    return method;
  }

  /// Parse performance mode string to enum
  dynamic _parsePerformanceMode(String mode) {
    // This would parse the string to the appropriate enum
    // Placeholder implementation
    return mode;
  }

  // ==================== DEFAULT DATA METHODS ====================

  /// Get default theme data for fallback
  ThemeSettingsState _getDefaultThemeData() {
    return const ThemeSettingsState();
  }

  /// Get default notification data for fallback
  NotificationSettingsState _getDefaultNotificationData() {
    return const NotificationSettingsState();
  }

  /// Get default prayer times data for fallback
  PrayerTimesSettingsState _getDefaultPrayerTimesData() {
    return const PrayerTimesSettingsState();
  }

  /// Get default location data for fallback
  LocationSettingsState _getDefaultLocationData() {
    return const LocationSettingsState();
  }

  /// Get default performance data for fallback
  PerformanceSettingsState _getDefaultPerformanceData() {
    return const PerformanceSettingsState();
  }

  /// Get default advanced data for fallback
  AdvancedSettingsState _getDefaultAdvancedData() {
    return const AdvancedSettingsState();
  }

  // ==================== TYPE CONVERSION METHODS ====================

  /// Convert new NotificationSound to legacy NotificationSound
  legacy.NotificationSound _convertNotificationSound(NotificationSound newSound) {
    switch (newSound) {
      case NotificationSound.defaultSound:
        return legacy.NotificationSound.defaultSound;
      case NotificationSound.adhan:
        return legacy.NotificationSound.adhan;
      case NotificationSound.bell:
        return legacy.NotificationSound.bell;
      case NotificationSound.chime:
        return legacy.NotificationSound.chime;
      case NotificationSound.silent:
        return legacy.NotificationSound.silent;
    }
  }

  /// Convert new NotificationTiming to legacy NotificationTiming
  legacy.NotificationTiming _convertNotificationTiming(NotificationTiming newTiming) {
    switch (newTiming) {
      case NotificationTiming.exactTime:
        return legacy.NotificationTiming.exactTime;
      case NotificationTiming.fiveMinutesBefore:
      case NotificationTiming.tenMinutesBefore:
      case NotificationTiming.fifteenMinutesBefore:
      case NotificationTiming.thirtyMinutesBefore:
        return legacy.NotificationTiming.beforePrayer;
    }
  }

  /// Convert unified notification settings to legacy format
  NotificationSettingsState _convertUnifiedToLegacyNotificationSettings(
    unified.NotificationSettingsState unifiedSettings,
  ) {
    return NotificationSettingsState(
      notificationsEnabled: unifiedSettings.generalSettings.globallyEnabled,
      globalTiming: NotificationTiming.exactTime, // Default timing for compatibility
      useVibration: unifiedSettings.generalSettings.useSystemVibration,
      showOnLockScreen: unifiedSettings.generalSettings.showInForeground,
    );
  }
}
