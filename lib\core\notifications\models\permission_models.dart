import 'package:flutter/foundation.dart';

/// Permission Status State
///
/// Comprehensive state object representing the current permission status
/// following Context7 MCP state management patterns.
class PermissionStatusState {
  final PermissionCheckResult permissionCheck;
  final PermissionHealthStatus healthStatus;
  final List<String> recommendations;
  final PermissionMonitoringStatus monitoringStatus;
  final DateTime lastUpdated;
  final bool isMonitoring;
  final Map<String, dynamic> metadata;

  const PermissionStatusState({
    required this.permissionCheck,
    required this.healthStatus,
    required this.recommendations,
    required this.monitoringStatus,
    required this.lastUpdated,
    required this.isMonitoring,
    this.metadata = const {},
  });

  /// Check if state is healthy
  bool get isHealthy => healthStatus == PermissionHealthStatus.excellent || healthStatus == PermissionHealthStatus.good;

  /// Check if state requires immediate attention
  bool get requiresAttention =>
      healthStatus == PermissionHealthStatus.critical || healthStatus == PermissionHealthStatus.poor;

  /// Get overall permission grant percentage
  double get grantPercentage => permissionCheck.grantPercentage;

  /// Check if monitoring is active and healthy
  bool get isMonitoringHealthy => isMonitoring && monitoringStatus.isHealthy;

  /// Get time since last update
  Duration get timeSinceUpdate => DateTime.now().difference(lastUpdated);

  /// Check if state is stale (older than 10 minutes)
  bool get isStale => timeSinceUpdate > const Duration(minutes: 10);

  /// Create copy with updated values
  PermissionStatusState copyWith({
    PermissionCheckResult? permissionCheck,
    PermissionHealthStatus? healthStatus,
    List<String>? recommendations,
    PermissionMonitoringStatus? monitoringStatus,
    DateTime? lastUpdated,
    bool? isMonitoring,
    Map<String, dynamic>? metadata,
  }) {
    return PermissionStatusState(
      permissionCheck: permissionCheck ?? this.permissionCheck,
      healthStatus: healthStatus ?? this.healthStatus,
      recommendations: recommendations ?? this.recommendations,
      monitoringStatus: monitoringStatus ?? this.monitoringStatus,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      isMonitoring: isMonitoring ?? this.isMonitoring,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Permission Health Summary
///
/// Summary of permission health status for UI display
/// following Context7 MCP UI data patterns.
class PermissionHealthSummary {
  final PermissionHealthStatus overallHealth;
  final int totalPermissions;
  final int grantedPermissions;
  final int deniedPermissions;
  final int criticalIssues;
  final List<String> topRecommendations;
  final bool isMonitoring;
  final DateTime lastChecked;

  const PermissionHealthSummary({
    required this.overallHealth,
    required this.totalPermissions,
    required this.grantedPermissions,
    required this.deniedPermissions,
    required this.criticalIssues,
    required this.topRecommendations,
    required this.isMonitoring,
    required this.lastChecked,
  });

  /// Create unknown health summary
  factory PermissionHealthSummary.unknown() {
    return PermissionHealthSummary(
      overallHealth: PermissionHealthStatus.unknown,
      totalPermissions: 0,
      grantedPermissions: 0,
      deniedPermissions: 0,
      criticalIssues: 0,
      topRecommendations: ['Permission status unknown'],
      isMonitoring: false,
      lastChecked: DateTime.now(),
    );
  }

  /// Get health percentage (0.0 to 1.0)
  double get healthPercentage {
    if (totalPermissions == 0) return 1.0;
    return grantedPermissions / totalPermissions;
  }

  /// Get health color for UI
  String get healthColor {
    switch (overallHealth) {
      case PermissionHealthStatus.excellent:
        return '#4CAF50'; // Green
      case PermissionHealthStatus.good:
        return '#8BC34A'; // Light Green
      case PermissionHealthStatus.fair:
        return '#FFC107'; // Amber
      case PermissionHealthStatus.poor:
        return '#FF9800'; // Orange
      case PermissionHealthStatus.critical:
        return '#F44336'; // Red
      case PermissionHealthStatus.unknown:
        return '#9E9E9E'; // Grey
    }
  }

  /// Get health description
  String get healthDescription {
    switch (overallHealth) {
      case PermissionHealthStatus.excellent:
        return 'All permissions are properly configured';
      case PermissionHealthStatus.good:
        return 'Most permissions are working well';
      case PermissionHealthStatus.fair:
        return 'Some permissions need attention';
      case PermissionHealthStatus.poor:
        return 'Several permissions are missing';
      case PermissionHealthStatus.critical:
        return 'Critical permissions are denied';
      case PermissionHealthStatus.unknown:
        return 'Permission status is unknown';
    }
  }

  /// Check if immediate action is required
  bool get requiresImmediateAction => overallHealth == PermissionHealthStatus.critical;

  /// Check if monitoring is healthy
  bool get isMonitoringHealthy => isMonitoring;
}

/// Permission Notification Type
///
/// Enum representing different types of notification permissions
/// following Context7 MCP comprehensive permission management patterns.
enum PermissionNotificationType {
  /// Local notifications (basic app notifications)
  local,

  /// Push notifications (remote notifications)
  push,

  /// Scheduled notifications (time-based notifications)
  scheduled,

  /// Background notifications (notifications when app is not active)
  background,

  /// Critical alerts (high-priority system notifications)
  critical,

  /// Provisional notifications (iOS quiet notifications)
  provisional,
}

/// Permission Status
///
/// Enum representing the status of a specific permission
/// following Context7 MCP permission state management patterns.
enum PermissionStatus {
  /// Permission has been granted
  granted,

  /// Permission has been denied
  denied,

  /// Permission has been permanently denied (requires settings)
  permanentlyDenied,

  /// Permission is restricted by system policy
  restricted,

  /// Permission is limited (iOS 14+ limited access)
  limited,

  /// Permission status is unknown
  unknown,
}

/// Permission Health Status
///
/// Enum representing the overall health of notification permissions
/// following Context7 MCP health monitoring patterns.
enum PermissionHealthStatus {
  /// All permissions are properly configured
  excellent,

  /// Most permissions are working well
  good,

  /// Some permissions need attention
  fair,

  /// Several permissions are missing
  poor,

  /// Critical permissions are denied
  critical,

  /// Permission status is unknown
  unknown,
}

/// Channel Permission Status
///
/// Represents the permission status for a specific notification channel
/// following Context7 MCP granular permission tracking patterns.
class ChannelPermissionStatus {
  final String channelId;
  final String channelName;
  final PermissionStatus status;
  final bool soundEnabled;
  final bool vibrationEnabled;
  final bool badgeEnabled;
  final DateTime lastChecked;

  const ChannelPermissionStatus({
    required this.channelId,
    required this.channelName,
    required this.status,
    required this.soundEnabled,
    required this.vibrationEnabled,
    required this.badgeEnabled,
    required this.lastChecked,
  });

  /// Check if channel is fully enabled
  bool get isFullyEnabled => status == PermissionStatus.granted && soundEnabled && vibrationEnabled && badgeEnabled;

  /// Check if channel has basic functionality
  bool get hasBasicFunctionality => status == PermissionStatus.granted;

  /// Get channel health score (0.0 to 1.0)
  double get healthScore {
    if (status != PermissionStatus.granted) return 0.0;

    var enabledFeatures = 0;
    if (soundEnabled) enabledFeatures++;
    if (vibrationEnabled) enabledFeatures++;
    if (badgeEnabled) enabledFeatures++;

    return enabledFeatures / 3.0;
  }
}

/// Permission Check Result
///
/// Comprehensive result of permission checking operations
/// following Context7 MCP detailed result reporting patterns.
class PermissionCheckResult {
  final bool hasAllRequired;
  final List<PermissionNotificationType> grantedPermissions;
  final List<PermissionNotificationType> deniedPermissions;
  final List<PermissionNotificationType> permanentlyDeniedPermissions;
  final List<PermissionNotificationType> requestedTypes;
  final Map<PermissionNotificationType, PermissionStatus> permissionResults;
  final List<ChannelPermissionStatus> channelStatuses;
  final DateTime checkTime;
  final Map<String, dynamic> systemInfo;

  const PermissionCheckResult({
    required this.hasAllRequired,
    required this.grantedPermissions,
    required this.deniedPermissions,
    required this.permanentlyDeniedPermissions,
    required this.requestedTypes,
    required this.permissionResults,
    required this.channelStatuses,
    required this.checkTime,
    required this.systemInfo,
  });

  /// Get permission grant percentage
  double get grantPercentage {
    if (requestedTypes.isEmpty) return 1.0;
    return grantedPermissions.length / requestedTypes.length;
  }

  /// Check if any permissions are permanently denied
  bool get hasPermanentlyDenied => permanentlyDeniedPermissions.isNotEmpty;

  /// Get overall health status
  PermissionHealthStatus get healthStatus {
    if (hasAllRequired) return PermissionHealthStatus.excellent;
    if (grantPercentage >= 0.8) return PermissionHealthStatus.good;
    if (grantPercentage >= 0.6) return PermissionHealthStatus.fair;
    if (grantPercentage >= 0.3) return PermissionHealthStatus.poor;
    return PermissionHealthStatus.critical;
  }

  /// Check if results are stale (older than 5 minutes)
  bool get isStale => DateTime.now().difference(checkTime) > const Duration(minutes: 5);
}

/// Permission Request Result
///
/// Result of permission request operations with detailed information
/// following Context7 MCP comprehensive request result patterns.
class PermissionRequestResult {
  final bool success;
  final List<PermissionNotificationType> grantedPermissions;
  final List<PermissionNotificationType> deniedPermissions;
  final List<PermissionNotificationType> permanentlyDeniedPermissions;
  final Map<PermissionNotificationType, PermissionStatus> results;
  final bool userCancelled;
  final bool requiresSettings;
  final String? errorMessage;
  final DateTime requestTime;
  final Duration requestDuration;

  const PermissionRequestResult({
    required this.success,
    required this.grantedPermissions,
    required this.deniedPermissions,
    required this.permanentlyDeniedPermissions,
    required this.results,
    required this.userCancelled,
    required this.requiresSettings,
    this.errorMessage,
    required this.requestTime,
    required this.requestDuration,
  });

  /// Check if all requested permissions were granted
  bool get allGranted => deniedPermissions.isEmpty && permanentlyDeniedPermissions.isEmpty;

  /// Check if any permissions need settings intervention
  bool get needsSettingsIntervention => permanentlyDeniedPermissions.isNotEmpty || requiresSettings;

  /// Get success rate
  double get successRate {
    final totalRequested = grantedPermissions.length + deniedPermissions.length + permanentlyDeniedPermissions.length;
    if (totalRequested == 0) return 1.0;
    return grantedPermissions.length / totalRequested;
  }
}

/// Permission Status Report
///
/// Comprehensive permission status report with health analysis
/// following Context7 MCP detailed reporting patterns.
class PermissionStatusReport {
  final PermissionCheckResult permissionCheck;
  final PermissionHealthStatus overallHealth;
  final List<String> recommendations;
  final List<String> warnings;
  final List<String> criticalIssues;
  final DateTime reportTime;
  final Map<String, dynamic> metadata;

  const PermissionStatusReport({
    required this.permissionCheck,
    required this.overallHealth,
    required this.recommendations,
    required this.warnings,
    required this.criticalIssues,
    required this.reportTime,
    required this.metadata,
  });

  /// Check if report indicates healthy permissions
  bool get isHealthy =>
      overallHealth == PermissionHealthStatus.excellent || overallHealth == PermissionHealthStatus.good;

  /// Check if immediate action is required
  bool get requiresImmediateAction => criticalIssues.isNotEmpty || overallHealth == PermissionHealthStatus.critical;

  /// Get priority level for addressing issues
  int get priorityLevel {
    switch (overallHealth) {
      case PermissionHealthStatus.critical:
        return 1; // Highest priority
      case PermissionHealthStatus.poor:
        return 2;
      case PermissionHealthStatus.fair:
        return 3;
      case PermissionHealthStatus.good:
        return 4;
      case PermissionHealthStatus.excellent:
        return 5; // Lowest priority
      case PermissionHealthStatus.unknown:
        return 3; // Medium priority - needs investigation
    }
  }
}

/// Permission Monitoring Status
///
/// Status of permission monitoring system
/// following Context7 MCP monitoring state patterns.
class PermissionMonitoringStatus {
  final bool isActive;
  final DateTime? lastCheck;
  final Duration checkInterval;
  final int totalChecks;
  final int failedChecks;
  final bool backgroundMonitoring;
  final bool healthReporting;
  final Map<String, dynamic> statistics;

  const PermissionMonitoringStatus({
    required this.isActive,
    this.lastCheck,
    required this.checkInterval,
    required this.totalChecks,
    required this.failedChecks,
    required this.backgroundMonitoring,
    required this.healthReporting,
    required this.statistics,
  });

  /// Get monitoring health percentage
  double get healthPercentage {
    if (totalChecks == 0) return 1.0;
    return (totalChecks - failedChecks) / totalChecks;
  }

  /// Check if monitoring is healthy
  bool get isHealthy => healthPercentage >= 0.9 && isActive;

  /// Get time since last check
  Duration? get timeSinceLastCheck => lastCheck != null ? DateTime.now().difference(lastCheck!) : null;

  /// Check if monitoring is overdue
  bool get isOverdue => timeSinceLastCheck != null && timeSinceLastCheck! > checkInterval * 2;
}

/// Permission Change Event
///
/// Event representing a change in permission status
/// following Context7 MCP event-driven architecture patterns.
class PermissionChangeEvent {
  final PermissionNotificationType type;
  final PermissionStatus previousStatus;
  final PermissionStatus newStatus;
  final DateTime timestamp;
  final String reason;
  final Map<String, dynamic> metadata;

  const PermissionChangeEvent({
    required this.type,
    required this.previousStatus,
    required this.newStatus,
    required this.timestamp,
    required this.reason,
    required this.metadata,
  });

  /// Check if change represents an improvement
  bool get isImprovement => _getStatusPriority(newStatus) > _getStatusPriority(previousStatus);

  /// Check if change represents a degradation
  bool get isDegradation => _getStatusPriority(newStatus) < _getStatusPriority(previousStatus);

  /// Get change severity level
  int get severityLevel {
    if (newStatus == PermissionStatus.permanentlyDenied) return 3; // Critical
    if (newStatus == PermissionStatus.denied) return 2; // High
    if (newStatus == PermissionStatus.granted) return 1; // Low (positive change)
    return 1; // Default
  }

  /// Get status priority for comparison
  int _getStatusPriority(PermissionStatus status) {
    switch (status) {
      case PermissionStatus.granted:
        return 5;
      case PermissionStatus.limited:
        return 4;
      case PermissionStatus.unknown:
        return 3;
      case PermissionStatus.restricted:
        return 2;
      case PermissionStatus.denied:
        return 1;
      case PermissionStatus.permanentlyDenied:
        return 0;
    }
  }
}
