// ignore_for_file: public_member_api_docs

import 'package:riverpod_annotation/riverpod_annotation.dart';

// All required types are already available in notification_settings_state.dart
import '../../notifications/providers/unified_notification_provider.dart' as unified;
import 'notification_settings_state.dart';

part 'notification_settings_selectors.g.dart';

/// Notification Settings Selectors
///
/// Performance-optimized selectors following Context7 MCP best practices.
/// These selectors use Riverpod's `select` functionality to prevent unnecessary
/// rebuilds by only listening to specific properties of the notification state.
///
/// **Key Benefits:**
/// - **Performance**: Only rebuilds when selected properties change
/// - **Granular Control**: Each selector targets specific data
/// - **Type Safety**: Strong typing with proper error handling
/// - **Reactive**: Automatically updates when underlying state changes
/// - **Testable**: Each selector can be tested independently
///
/// **Usage Example:**
/// ```dart
/// // In a widget
/// final isEnabled = ref.watch(notificationsEnabledSelector);
/// final count = ref.watch(enabledNotificationsCountSelector);
///
/// // In a provider
/// final enabled = ref.watch(notificationsEnabledSelector);
/// ```

// ==================== CORE NOTIFICATION SELECTORS ====================

/// Global notifications enabled selector
///
/// **Purpose**: Provides the global notification toggle state
/// **Rebuilds**: Only when global notifications enabled/disabled
/// **Returns**: Boolean indicating if notifications are globally enabled
/// **Default**: `true` during loading or error states
@riverpod
bool notificationsEnabled(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => settings.generalSettings.globallyEnabled,
    loading: () => true, // Default to enabled during loading
    error: (_, _) => true, // Default to enabled on error
  );
}

/// Global notification timing selector
///
/// **Purpose**: Provides the default timing preference for notifications
/// **Rebuilds**: Only when global timing preference changes
/// **Returns**: NotificationTiming enum value
/// **Default**: `NotificationTiming.exactTime` during loading or error
@riverpod
NotificationTiming globalNotificationTiming(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => settings.globalTiming,
    loading: () => NotificationTiming.exactTime,
    error: (_, _) => NotificationTiming.exactTime,
  );
}

/// Global notification sound selector
///
/// **Purpose**: Provides the default sound preference for notifications
/// **Rebuilds**: Only when global sound preference changes
/// **Returns**: NotificationSound enum value
/// **Default**: `NotificationSound.adhan` during loading or error
@riverpod
NotificationSound globalNotificationSound(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => settings.generalSettings.globalSound,
    loading: () => NotificationSound.adhan,
    error: (_, _) => NotificationSound.adhan,
  );
}

/// Global vibration enabled selector
///
/// **Purpose**: Provides the global vibration preference
/// **Rebuilds**: Only when global vibration setting changes
/// **Returns**: Boolean indicating if vibration is enabled globally
/// **Default**: `true` during loading or error states
@riverpod
bool globalVibrationEnabled(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => settings.generalSettings.useSystemVibration,
    loading: () => true,
    error: (_, _) => true,
  );
}

// ==================== PRAYER-SPECIFIC SELECTORS ====================

/// Prayer notifications map selector
///
/// **Purpose**: Provides the complete map of prayer notification preferences
/// **Rebuilds**: Only when any prayer notification preference changes
/// **Returns**: Map of PrayerType to PrayerNotificationPreferences
/// **Default**: Empty map during loading or error states
@riverpod
Map<PrayerType, PrayerNotificationPreferences> prayerNotifications(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => <PrayerType, PrayerNotificationPreferences>{}, // TODO: Map from unified settings
    loading: () => <PrayerType, PrayerNotificationPreferences>{},
    error: (_, _) => <PrayerType, PrayerNotificationPreferences>{},
  );
}

/// Enabled prayers list selector
///
/// **Purpose**: Provides a list of prayers that have notifications enabled
/// **Rebuilds**: Only when prayer notification enabled states change
/// **Returns**: List of PrayerType values with notifications enabled
/// **Performance**: Computed from prayer notifications map
@riverpod
List<PrayerType> enabledPrayers(Ref ref) {
  final prayerNotifs = ref.watch(prayerNotificationsProvider);
  final globalEnabled = ref.watch(notificationsEnabledProvider);

  if (!globalEnabled) return [];

  return prayerNotifs.entries.where((entry) => entry.value.enabled).map((entry) => entry.key).toList();
}

/// Disabled prayers list selector
///
/// **Purpose**: Provides a list of prayers that have notifications disabled
/// **Rebuilds**: Only when prayer notification enabled states change
/// **Returns**: List of PrayerType values with notifications disabled
/// **Performance**: Computed from all prayers minus enabled prayers
@riverpod
List<PrayerType> disabledPrayers(Ref ref) {
  final enabledPrayersList = ref.watch(enabledPrayersProvider);
  return PrayerType.values.where((prayer) => !enabledPrayersList.contains(prayer)).toList();
}

// ==================== COUNT AND STATISTICS SELECTORS ====================

/// Enabled notifications count selector
///
/// **Purpose**: Provides the count of prayers with notifications enabled
/// **Rebuilds**: Only when the count of enabled prayers changes
/// **Returns**: Integer count of enabled prayer notifications
/// **Performance**: Computed from enabled prayers list length
@riverpod
int enabledNotificationsCount(Ref ref) {
  return ref.watch(enabledPrayersProvider).length;
}

/// Total prayers count selector
///
/// **Purpose**: Provides the total count of available prayers
/// **Rebuilds**: Never (static value)
/// **Returns**: Integer count of total available prayers
/// **Performance**: Constant value from PrayerType enum
@riverpod
int totalPrayersCount(Ref ref) {
  return PrayerType.values.length;
}

/// Notification coverage percentage selector
///
/// **Purpose**: Provides the percentage of prayers with notifications enabled
/// **Rebuilds**: Only when enabled count changes
/// **Returns**: Double percentage (0.0 to 100.0)
/// **Performance**: Computed from enabled count / total count
@riverpod
double notificationCoveragePercentage(Ref ref) {
  final enabledCount = ref.watch(enabledNotificationsCountProvider);
  final totalCount = ref.watch(totalPrayersCountProvider);

  if (totalCount == 0) return 0.0;
  return (enabledCount / totalCount) * 100.0;
}

// ==================== SPECIFIC PRAYER SELECTORS ====================

/// Individual prayer notification enabled selector
///
/// **Purpose**: Provides notification enabled state for a specific prayer
/// **Parameters**: `prayerType` - The prayer to check
/// **Rebuilds**: Only when the specific prayer's enabled state changes
/// **Returns**: Boolean indicating if the prayer has notifications enabled
/// **Performance**: Uses family modifier for parameter-based selection
@riverpod
bool prayerNotificationEnabled(Ref ref, PrayerType prayerType) {
  final globalEnabled = ref.watch(notificationsEnabledProvider);
  if (!globalEnabled) return false;

  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => false, // TODO: Map from unified settings
    loading: () => false,
    error: (_, _) => false,
  );
}

/// Individual prayer notification preferences selector
///
/// **Purpose**: Provides complete notification preferences for a specific prayer
/// **Parameters**: `prayerType` - The prayer to get preferences for
/// **Rebuilds**: Only when the specific prayer's preferences change
/// **Returns**: PrayerNotificationPreferences for the specified prayer
/// **Performance**: Uses family modifier for parameter-based selection
@riverpod
PrayerNotificationPreferences prayerNotificationPreferences(Ref ref, PrayerType prayerType) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => const PrayerNotificationPreferences(), // TODO: Map from unified settings
    loading: () => const PrayerNotificationPreferences(),
    error: (_, _) => const PrayerNotificationPreferences(),
  );
}

/// Effective sound for prayer selector
///
/// **Purpose**: Provides the effective sound for a specific prayer
/// **Parameters**: `prayerType` - The prayer to get sound for
/// **Rebuilds**: Only when the prayer's sound or global sound changes
/// **Returns**: NotificationSound that will be used for the prayer
/// **Logic**: Returns custom sound if set, otherwise global sound
@riverpod
NotificationSound effectivePrayerSound(Ref ref, PrayerType prayerType) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => settings.globalSound, // Use global sound for now
    loading: () => NotificationSound.adhan,
    error: (_, _) => NotificationSound.adhan,
  );
}

// ==================== ADVANCED FEATURE SELECTORS ====================

/// Quiet hours enabled selector
///
/// **Purpose**: Provides whether quiet hours are configured
/// **Rebuilds**: Only when quiet hours configuration changes
/// **Returns**: Boolean indicating if quiet hours are set
/// **Logic**: Checks if quietHours object is not null
@riverpod
bool quietHoursEnabled(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => false, // TODO: Map quiet hours from unified settings
    loading: () => false,
    error: (_, _) => false,
  );
}

/// Show on lock screen enabled selector
///
/// **Purpose**: Provides the lock screen display preference
/// **Rebuilds**: Only when lock screen setting changes
/// **Returns**: Boolean indicating if notifications show on lock screen
/// **Default**: `true` during loading or error states
@riverpod
bool showOnLockScreenEnabled(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => settings.generalSettings.showPreviewsOnLockScreen,
    loading: () => true,
    error: (_, _) => true,
  );
}

/// Persistent notifications enabled selector
///
/// **Purpose**: Provides the persistent notifications preference
/// **Rebuilds**: Only when persistent notifications setting changes
/// **Returns**: Boolean indicating if notifications are persistent
/// **Default**: `false` during loading or error states
@riverpod
bool persistentNotificationsEnabled(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => false, // TODO: Map persistent notifications from unified settings
    loading: () => false,
    error: (_, _) => false,
  );
}

// ==================== VALIDATION AND STATUS SELECTORS ====================

/// Notification settings valid selector
///
/// **Purpose**: Provides validation status of current notification settings
/// **Rebuilds**: Only when validation status changes
/// **Returns**: Boolean indicating if settings are valid
/// **Logic**: Uses the state's validation method
@riverpod
bool notificationSettingsValid(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => true, // TODO: Map validation from unified settings
    loading: () => true, // Assume valid during loading
    error: (_, _) => false, // Invalid on error
  );
}

/// Has custom prayer settings selector
///
/// **Purpose**: Indicates if any prayers have custom settings different from global
/// **Rebuilds**: Only when custom settings status changes
/// **Returns**: Boolean indicating if any prayers have custom settings
/// **Performance**: Computed from prayer notifications analysis
@riverpod
bool hasCustomPrayerSettings(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => false, // TODO: Map custom settings from unified
    loading: () => false,
    error: (_, _) => false,
  );
}

/// Prayers with custom vibration selector
///
/// **Purpose**: Provides list of prayers with custom vibration settings
/// **Rebuilds**: Only when custom vibration settings change
/// **Returns**: List of PrayerType values with custom vibration
/// **Performance**: Computed from prayer notifications analysis
@riverpod
List<PrayerType> prayersWithCustomVibration(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => <PrayerType>[], // TODO: Map from unified
    loading: () => <PrayerType>[],
    error: (_, _) => <PrayerType>[],
  );
}

/// Prayers with custom sound selector
///
/// **Purpose**: Provides list of prayers with custom sound settings
/// **Rebuilds**: Only when custom sound settings change
/// **Returns**: List of PrayerType values with custom sounds
/// **Performance**: Computed from prayer notifications analysis
@riverpod
List<PrayerType> prayersWithCustomSound(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => <PrayerType>[], // TODO: Map from unified
    loading: () => <PrayerType>[],
    error: (_, _) => <PrayerType>[],
  );
}

// ==================== CONVENIENCE SELECTORS ====================

/// All notification settings selector
///
/// **Purpose**: Provides the complete notification settings state
/// **Rebuilds**: When any notification setting changes
/// **Returns**: Complete NotificationSettingsState object
/// **Usage**: For components that need access to multiple settings
/// **Note**: Use specific selectors when possible for better performance
@riverpod
NotificationSettingsState? allNotificationSettings(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.when(
    data: (settings) => null, // TODO: Map from unified
    loading: () => null,
    error: (_, _) => null,
  );
}

/// Notification settings loading selector
///
/// **Purpose**: Provides the loading state of notification settings
/// **Rebuilds**: Only when loading state changes
/// **Returns**: Boolean indicating if settings are currently loading
/// **Usage**: For showing loading indicators
@riverpod
bool notificationSettingsLoading(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.isLoading;
}

/// Notification settings error selector
///
/// **Purpose**: Provides any error that occurred while loading settings
/// **Rebuilds**: Only when error state changes
/// **Returns**: Error object or null if no error
/// **Usage**: For showing error messages
@riverpod
Object? notificationSettingsError(Ref ref) {
  final asyncSettings = ref.watch(unified.unifiedNotificationSettingsProvider);
  return asyncSettings.error;
}
