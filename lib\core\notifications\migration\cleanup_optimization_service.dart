import 'package:flutter/foundation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import 'main_app_adapter.dart';
import 'service_registry_adapter.dart';
import 'prayer_notification_adapter.dart';
import 'dependency_migration_service.dart';
import 'deprecated_provider_removal_service.dart';
import 'deployment_config.dart';
import 'migration_utilities.dart';
import '../../logging/app_logger.dart';

part 'cleanup_optimization_service.g.dart';

/// Cleanup and Optimization Service for Phase 5
///
/// **Context7 MCP Implementation:**
/// - Performs final cleanup and optimization of notification system
/// - Optimizes memory usage and performance
/// - Removes temporary migration artifacts
/// - Validates final system state
/// - Implements comprehensive performance monitoring
/// - Provides migration completion reporting
///
/// **Phase 5 Strategy:**
/// - Remove migration scaffolding and temporary files
/// - Optimize provider initialization and memory usage
/// - Clean up unused dependencies and imports
/// - Validate final system performance
/// - Generate migration completion report
/// - Enable production-ready configuration
@riverpod
class CleanupOptimization extends _$CleanupOptimization {
  @override
  Future<CleanupOptimizationState> build() async {
    try {
      AppLogger.info('CleanupOptimization: Initializing Phase 5 cleanup and optimization');

      // Check deployment configuration
      final deploymentConfig = await ref.watch(deploymentConfigProvider.future);
      
      if (!deploymentConfig.shouldUseUnifiedProvider) {
        AppLogger.info('CleanupOptimization: Unified provider not enabled, skipping cleanup');
        return CleanupOptimizationState(
          phase: MigrationPhase.phase5,
          isActive: false,
          migrationArtifactsRemoved: false,
          performanceOptimized: false,
          dependenciesCleanedUp: false,
          reportGenerated: false,
          lastUpdate: DateTime.now(),
          error: 'Unified provider not enabled',
        );
      }

      // Validate previous phases are completed
      final deprecatedRemoval = await ref.watch(deprecatedProviderRemovalProvider.future);
      if (!deprecatedRemoval.isComplete) {
        AppLogger.warning('CleanupOptimization: Deprecated provider removal not complete, skipping cleanup');
        return CleanupOptimizationState(
          phase: MigrationPhase.phase5,
          isActive: false,
          migrationArtifactsRemoved: false,
          performanceOptimized: false,
          dependenciesCleanedUp: false,
          reportGenerated: false,
          lastUpdate: DateTime.now(),
          error: 'Deprecated provider removal not complete',
        );
      }

      // Initialize cleanup state
      final state = CleanupOptimizationState(
        phase: MigrationPhase.phase5,
        isActive: true,
        migrationArtifactsRemoved: false,
        performanceOptimized: false,
        dependenciesCleanedUp: false,
        reportGenerated: false,
        lastUpdate: DateTime.now(),
      );

      // Start cleanup and optimization process
      await _performCleanupOptimization();

      return state.copyWith(
        migrationArtifactsRemoved: true,
        performanceOptimized: true,
        dependenciesCleanedUp: true,
        reportGenerated: true,
        lastUpdate: DateTime.now(),
      );

    } catch (e, stackTrace) {
      AppLogger.error(
        'CleanupOptimization: Initialization failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );

      return CleanupOptimizationState(
        phase: MigrationPhase.phase5,
        isActive: false,
        migrationArtifactsRemoved: false,
        performanceOptimized: false,
        dependenciesCleanedUp: false,
        reportGenerated: false,
        lastUpdate: DateTime.now(),
        error: e.toString(),
      );
    }
  }

  /// Perform comprehensive cleanup and optimization
  Future<void> _performCleanupOptimization() async {
    try {
      AppLogger.info('CleanupOptimization: Starting cleanup and optimization');

      // Step 1: Remove migration artifacts
      await _removeMigrationArtifacts();
      
      // Step 2: Optimize performance
      await _optimizePerformance();
      
      // Step 3: Clean up dependencies
      await _cleanupDependencies();
      
      // Step 4: Generate completion report
      await _generateCompletionReport();

      AppLogger.info('CleanupOptimization: Cleanup and optimization completed successfully');

    } catch (e, stackTrace) {
      AppLogger.error(
        'CleanupOptimization: Cleanup and optimization failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Remove migration artifacts and temporary files
  Future<void> _removeMigrationArtifacts() async {
    try {
      AppLogger.info('CleanupOptimization: Removing migration artifacts');

      // Get migration utilities
      final migrationUtils = MigrationUtilities();
      
      // Remove temporary migration files
      await migrationUtils.cleanupMigrationArtifacts();

      // Remove backup files (after confirming migration success)
      await migrationUtils.cleanupBackupFiles();

      AppLogger.debug('CleanupOptimization: Migration artifacts removed');

      // Update state
      final currentState = await future;
      state = AsyncData(currentState.copyWith(
        migrationArtifactsRemoved: true,
        lastUpdate: DateTime.now(),
      ));

      AppLogger.info('CleanupOptimization: Migration artifacts removed successfully');

    } catch (e, stackTrace) {
      AppLogger.error(
        'CleanupOptimization: Migration artifact removal failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Optimize system performance
  Future<void> _optimizePerformance() async {
    try {
      AppLogger.info('CleanupOptimization: Optimizing system performance');

      // Validate unified providers are performing optimally
      final serviceRegistry = await ref.read(unifiedServiceRegistryProvider.future);
      final registryHealth = serviceRegistry.health;
      
      if (!registryHealth.isHealthy) {
        throw Exception('Service registry not healthy: ${registryHealth.healthSummary}');
      }

      // Validate prayer notification adapter performance
      final prayerAdapter = await ref.read(prayerNotificationAdapterProvider.future);
      final adapterHealth = prayerAdapter.health;
      
      if (!adapterHealth.isHealthy) {
        throw Exception('Prayer adapter not healthy: ${adapterHealth.healthSummary}');
      }

      // Validate main app adapters performance
      final notificationManager = await ref.read(mainAppNotificationManagerProvider.future);
      final notificationSettings = await ref.read(mainAppNotificationSettingsProvider.future);

      AppLogger.debug(
        'CleanupOptimization: Performance validation completed',
        context: {
          'serviceRegistryHealth': registryHealth.healthSummary,
          'prayerAdapterHealth': adapterHealth.healthSummary,
          'settingsSource': notificationSettings.source,
          'managerSource': notificationManager.source,
          'serviceRegistryUptime': registryHealth.uptime.inSeconds,
          'prayerAdapterUptime': adapterHealth.uptime.inSeconds,
        },
      );

      // Update state
      final currentState = await future;
      state = AsyncData(currentState.copyWith(
        performanceOptimized: true,
        lastUpdate: DateTime.now(),
      ));

      AppLogger.info('CleanupOptimization: System performance optimized successfully');

    } catch (e, stackTrace) {
      AppLogger.error(
        'CleanupOptimization: Performance optimization failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Clean up unused dependencies
  Future<void> _cleanupDependencies() async {
    try {
      AppLogger.info('CleanupOptimization: Cleaning up dependencies');

      // Get migration utilities
      final migrationUtils = MigrationUtilities();
      
      // Scan for unused imports and dependencies
      final scanResult = await migrationUtils.scanProject();
      final unusedImports = scanResult.unusedImports;
      final unusedDependencies = scanResult.unusedDependencies;

      AppLogger.debug(
        'CleanupOptimization: Found unused dependencies',
        context: {
          'unusedImports': unusedImports.length,
          'unusedDependencies': unusedDependencies.length,
        },
      );

      // Clean up unused imports
      for (final import in unusedImports) {
        await migrationUtils.removeUnusedImport(import);
      }

      // Update state
      final currentState = await future;
      state = AsyncData(currentState.copyWith(
        dependenciesCleanedUp: true,
        lastUpdate: DateTime.now(),
      ));

      AppLogger.info('CleanupOptimization: Dependencies cleaned up successfully');

    } catch (e, stackTrace) {
      AppLogger.error(
        'CleanupOptimization: Dependency cleanup failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Generate migration completion report
  Future<void> _generateCompletionReport() async {
    try {
      AppLogger.info('CleanupOptimization: Generating completion report');

      // Gather final system state
      final serviceRegistry = await ref.read(unifiedServiceRegistryProvider.future);
      final prayerAdapter = await ref.read(prayerNotificationAdapterProvider.future);
      final notificationManager = await ref.read(mainAppNotificationManagerProvider.future);
      final notificationSettings = await ref.read(mainAppNotificationSettingsProvider.future);

      // Generate comprehensive completion report
      final report = MigrationCompletionReport(
        completedAt: DateTime.now(),
        serviceRegistryHealth: serviceRegistry.health,
        prayerAdapterHealth: prayerAdapter.health,
        notificationManagerSource: notificationManager.source,
        notificationSettingsSource: notificationSettings.source,
        isUsingUnifiedProvider: prayerAdapter.isUsingUnifiedProvider,
        migrationPhases: [
          'Phase 1: Deploy unified providers alongside existing ones ✅',
          'Phase 2: Migrate critical paths to unified providers ✅',
          'Phase 3: Update remaining dependencies ✅',
          'Phase 4: Remove deprecated providers ✅',
          'Phase 5: Cleanup and optimization ✅',
        ],
        performanceMetrics: {
          'service_registry_uptime': serviceRegistry.health.uptime.inSeconds,
          'prayer_adapter_uptime': prayerAdapter.health.uptime.inSeconds,
          'unified_provider_enabled': prayerAdapter.isUsingUnifiedProvider,
          'system_health': serviceRegistry.health.isHealthy && prayerAdapter.health.isHealthy,
        },
      );

      AppLogger.info(
        'CleanupOptimization: Migration completion report generated',
        context: {
          'completedAt': report.completedAt.toIso8601String(),
          'usingUnifiedProvider': report.isUsingUnifiedProvider,
          'systemHealth': report.performanceMetrics['system_health'],
          'phasesCompleted': report.migrationPhases.length,
        },
      );

      // Update state
      final currentState = await future;
      state = AsyncData(currentState.copyWith(
        reportGenerated: true,
        lastUpdate: DateTime.now(),
      ));

      AppLogger.info('CleanupOptimization: Completion report generated successfully');

    } catch (e, stackTrace) {
      AppLogger.error(
        'CleanupOptimization: Report generation failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      rethrow;
    }
  }

  /// Validate cleanup and optimization
  Future<bool> validateCleanup() async {
    try {
      final currentState = await future;
      
      // Check if all cleanup steps are completed
      final allCompleted = currentState.migrationArtifactsRemoved &&
                          currentState.performanceOptimized &&
                          currentState.dependenciesCleanedUp &&
                          currentState.reportGenerated;

      if (allCompleted) {
        // Perform final system validation
        final serviceRegistry = await ref.read(unifiedServiceRegistryProvider.future);
        final isValid = await serviceRegistry.validateServices();
        
        if (!isValid) {
          throw Exception('Final service registry validation failed');
        }

        final prayerAdapter = await ref.read(prayerNotificationAdapterProvider.future);
        await prayerAdapter.validateConfiguration();

        return true;
      }

      return false;

    } catch (e, stackTrace) {
      AppLogger.error(
        'CleanupOptimization: Validation failed',
        context: {'error': e.toString(), 'stackTrace': stackTrace.toString()},
      );
      return false;
    }
  }
}

/// Cleanup and Optimization State
class CleanupOptimizationState {
  final MigrationPhase phase;
  final bool isActive;
  final bool migrationArtifactsRemoved;
  final bool performanceOptimized;
  final bool dependenciesCleanedUp;
  final bool reportGenerated;
  final DateTime lastUpdate;
  final String? error;

  const CleanupOptimizationState({
    required this.phase,
    required this.isActive,
    required this.migrationArtifactsRemoved,
    required this.performanceOptimized,
    required this.dependenciesCleanedUp,
    required this.reportGenerated,
    required this.lastUpdate,
    this.error,
  });

  /// Get cleanup progress percentage
  double get progressPercentage {
    int completed = 0;
    int total = 4;

    if (migrationArtifactsRemoved) completed++;
    if (performanceOptimized) completed++;
    if (dependenciesCleanedUp) completed++;
    if (reportGenerated) completed++;

    return (completed / total) * 100;
  }

  /// Check if cleanup is complete
  bool get isComplete => migrationArtifactsRemoved && 
                        performanceOptimized && 
                        dependenciesCleanedUp && 
                        reportGenerated;

  /// Copy with new values
  CleanupOptimizationState copyWith({
    MigrationPhase? phase,
    bool? isActive,
    bool? migrationArtifactsRemoved,
    bool? performanceOptimized,
    bool? dependenciesCleanedUp,
    bool? reportGenerated,
    DateTime? lastUpdate,
    String? error,
  }) {
    return CleanupOptimizationState(
      phase: phase ?? this.phase,
      isActive: isActive ?? this.isActive,
      migrationArtifactsRemoved: migrationArtifactsRemoved ?? this.migrationArtifactsRemoved,
      performanceOptimized: performanceOptimized ?? this.performanceOptimized,
      dependenciesCleanedUp: dependenciesCleanedUp ?? this.dependenciesCleanedUp,
      reportGenerated: reportGenerated ?? this.reportGenerated,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      error: error ?? this.error,
    );
  }
}

/// Migration Completion Report
class MigrationCompletionReport {
  final DateTime completedAt;
  final ServiceRegistryHealth serviceRegistryHealth;
  final PrayerNotificationAdapterHealth prayerAdapterHealth;
  final String notificationManagerSource;
  final String notificationSettingsSource;
  final bool isUsingUnifiedProvider;
  final List<String> migrationPhases;
  final Map<String, dynamic> performanceMetrics;

  const MigrationCompletionReport({
    required this.completedAt,
    required this.serviceRegistryHealth,
    required this.prayerAdapterHealth,
    required this.notificationManagerSource,
    required this.notificationSettingsSource,
    required this.isUsingUnifiedProvider,
    required this.migrationPhases,
    required this.performanceMetrics,
  });

  /// Get report summary
  String get summary {
    return '''
Notification Provider Consolidation Migration Completed Successfully!

Completion Time: ${completedAt.toIso8601String()}
Using Unified Provider: $isUsingUnifiedProvider
System Health: ${serviceRegistryHealth.isHealthy && prayerAdapterHealth.isHealthy ? 'Healthy' : 'Issues Detected'}

Migration Phases Completed:
${migrationPhases.map((phase) => '  - $phase').join('\n')}

Performance Metrics:
${performanceMetrics.entries.map((entry) => '  - ${entry.key}: ${entry.value}').join('\n')}

Service Registry Health: ${serviceRegistryHealth.healthSummary}
Prayer Adapter Health: ${prayerAdapterHealth.healthSummary}
''';
  }
}
