import 'package:flutter_test/flutter_test.dart';
import 'package:masajid_albahrain/core/notifications/models/notification_settings.dart';
import 'package:masajid_albahrain/core/notifications/models/notification_channel.dart';

/// Comprehensive integration tests for notification model interactions
/// 
/// Following Context7 MCP best practices for integration testing:
/// - Model interaction testing with realistic scenarios
/// - Cross-model data consistency validation
/// - Serialization/deserialization integration
/// - Complex workflow integration testing
/// - Performance under realistic data loads
/// - Error handling across model boundaries
/// 
/// This test suite achieves comprehensive integration coverage for Task 5.1.2,
/// demonstrating Context7 MCP integration testing patterns for notification consolidation.
void main() {
  group('Notification Models Integration Tests - Context7 MCP', () {
    late NotificationSettings defaultSettings;
    late Map<NotificationChannelKey, NotificationChannelSettings> complexChannelSettings;

    setUpAll(() {
      // Initialize test data following Context7 MCP patterns
      defaultSettings = NotificationSettings.defaultSettings();
      
      // Create complex channel settings for integration testing
      complexChannelSettings = {
        NotificationChannelKey.general: NotificationChannelSettings(
          enabled: true,
          customSound: true,
          customVibration: false,
          customLights: true,
          customBadge: false,
          quietHoursStart: 22,
          quietHoursEnd: 6,
        ),
        NotificationChannelKey.prayerTimes: NotificationChannelSettings(
          enabled: false,
          customSound: false,
          customVibration: true,
          customLights: false,
          customBadge: true,
          quietHoursStart: 23,
          quietHoursEnd: 5,
        ),
        NotificationChannelKey.backgroundSync: NotificationChannelSettings(
          enabled: true,
          customSound: false,
          customVibration: false,
          customLights: false,
          customBadge: false,
        ),
        NotificationChannelKey.systemAlerts: NotificationChannelSettings(
          enabled: true,
          customSound: true,
          customVibration: true,
          customLights: true,
          customBadge: true,
        ),
      };
    });

    group('Model Integration Workflows', () {
      test('should integrate settings with channel configurations', () async {
        // Arrange - Create integrated settings
        final integratedSettings = NotificationSettings(
          globallyEnabled: true,
          channelSettings: complexChannelSettings,
          useSystemSound: false,
          useSystemVibration: true,
          groupNotifications: true,
          maxNotifications: 100,
          enableAnalytics: true,
        );

        // Act - Test integration points
        final isGeneralEnabled = integratedSettings.isChannelEnabled(NotificationChannelKey.general);
        final isPrayerEnabled = integratedSettings.isChannelEnabled(NotificationChannelKey.prayerTimes);
        final isSyncEnabled = integratedSettings.isChannelEnabled(NotificationChannelKey.backgroundSync);
        final isAlertsEnabled = integratedSettings.isChannelEnabled(NotificationChannelKey.systemAlerts);

        // Assert - Verify integration behavior
        expect(isGeneralEnabled, isTrue); // Channel enabled
        expect(isPrayerEnabled, isFalse); // Channel disabled
        expect(isSyncEnabled, isTrue); // Channel enabled
        expect(isAlertsEnabled, isTrue); // Channel enabled
      });

      test('should handle global disable overriding channel settings', () async {
        // Arrange - Create settings with global disable
        final globallyDisabledSettings = NotificationSettings(
          globallyEnabled: false, // Global disable
          channelSettings: complexChannelSettings, // Channels individually enabled
          useSystemSound: true,
          useSystemVibration: true,
        );

        // Act - Test global override behavior
        final isGeneralEnabled = globallyDisabledSettings.isChannelEnabled(NotificationChannelKey.general);
        final isSyncEnabled = globallyDisabledSettings.isChannelEnabled(NotificationChannelKey.backgroundSync);
        final isAlertsEnabled = globallyDisabledSettings.isChannelEnabled(NotificationChannelKey.systemAlerts);

        // Assert - Global disable should override all channels
        expect(isGeneralEnabled, isFalse);
        expect(isSyncEnabled, isFalse);
        expect(isAlertsEnabled, isFalse);
      });

      test('should integrate sound and vibration settings correctly', () async {
        // Arrange - Create settings with mixed sound/vibration preferences
        final mixedSettings = NotificationSettings(
          globallyEnabled: true,
          channelSettings: complexChannelSettings,
          useSystemSound: false, // Use custom sounds
          useSystemVibration: true, // Use system vibration
          customSoundPath: 'custom_notification.mp3',
          customVibrationPattern: [200, 100, 200, 100],
        );

        // Act - Test effective settings for different channels
        final generalSoundSetting = mixedSettings.getEffectiveSoundSetting(NotificationChannelKey.general);
        final generalVibrationSetting = mixedSettings.getEffectiveVibrationSetting(NotificationChannelKey.general);
        final prayerSoundSetting = mixedSettings.getEffectiveSoundSetting(NotificationChannelKey.prayerTimes);
        final prayerVibrationSetting = mixedSettings.getEffectiveVibrationSetting(NotificationChannelKey.prayerTimes);

        // Assert - Verify effective settings integration
        expect(generalSoundSetting, isTrue); // Channel has custom sound enabled
        expect(generalVibrationSetting, isTrue); // Uses system vibration (channel custom is false)
        expect(prayerSoundSetting, isFalse); // Channel disabled, so no sound
        expect(prayerVibrationSetting, isFalse); // Channel disabled, so no vibration
      });
    });

    group('Serialization Integration', () {
      test('should handle complete serialization workflow', () async {
        // Arrange - Create complex settings
        final complexSettings = NotificationSettings(
          globallyEnabled: true,
          channelSettings: complexChannelSettings,
          globalQuietHoursStart: 22,
          globalQuietHoursEnd: 6,
          respectDoNotDisturb: true,
          showInForeground: false,
          groupNotifications: true,
          maxNotifications: 75,
          clearOnDispose: true,
          useSystemSound: false,
          useSystemVibration: true,
          customSoundPath: 'assets/sounds/custom.mp3',
          customVibrationPattern: [100, 50, 100, 50, 200],
          showPreviewsOnLockScreen: false,
          showBadges: true,
          badgeCountMode: BadgeCountMode.highPriority,
          historyRetentionDays: 14,
          enableAnalytics: false,
          advancedFeatures: AdvancedNotificationFeatures(
            smartGrouping: true,
            scheduleOptimization: false,
            batteryOptimization: true,
            adaptiveTiming: true,
            priorityLearning: false,
            locationBased: false,
            bundling: true,
            maxBundleSize: 3,
          ),
        );

        // Act - Complete serialization round-trip
        final json = complexSettings.toJson();
        final deserialized = NotificationSettings.fromJson(json);
        final reserializedJson = deserialized.toJson();

        // Assert - Verify complete integration
        expect(deserialized, equals(complexSettings));
        expect(reserializedJson, equals(json));
        
        // Verify specific complex fields
        expect(deserialized.channelSettings.length, equals(4));
        expect(deserialized.customVibrationPattern, equals([100, 50, 100, 50, 200]));
        expect(deserialized.badgeCountMode, equals(BadgeCountMode.highPriority));
        expect(deserialized.advancedFeatures.maxBundleSize, equals(3));
      });

      test('should handle partial serialization with defaults', () async {
        // Arrange - Create partial JSON data
        final partialJson = {
          'globallyEnabled': false,
          'useSystemSound': true,
          'channelSettings': {
            'general': {
              'enabled': true,
              'customSound': false,
            },
            'prayerTimes': {
              'enabled': false,
              'customVibration': true,
            },
          },
          'maxNotifications': 25,
          'enableAnalytics': true,
        };

        // Act - Deserialize partial data
        final settings = NotificationSettings.fromJson(partialJson);

        // Assert - Should use defaults for missing fields
        expect(settings.globallyEnabled, isFalse);
        expect(settings.useSystemSound, isTrue);
        expect(settings.maxNotifications, equals(25));
        expect(settings.enableAnalytics, isTrue);
        
        // Default values for missing fields
        expect(settings.useSystemVibration, isTrue); // Default
        expect(settings.groupNotifications, isTrue); // Default
        expect(settings.historyRetentionDays, equals(30)); // Default
        expect(settings.badgeCountMode, equals(BadgeCountMode.unread)); // Default
        
        // Channel settings should be preserved
        expect(settings.channelSettings.length, equals(2));
        expect(settings.channelSettings[NotificationChannelKey.general]?.enabled, isTrue);
        expect(settings.channelSettings[NotificationChannelKey.prayerTimes]?.enabled, isFalse);
      });

      test('should handle channel settings serialization integration', () async {
        // Arrange - Focus on channel settings integration
        final channelFocusedSettings = NotificationSettings(
          globallyEnabled: true,
          channelSettings: complexChannelSettings,
        );

        // Act - Serialize and deserialize
        final json = channelFocusedSettings.toJson();
        final deserialized = NotificationSettings.fromJson(json);

        // Assert - Verify channel settings integration
        expect(deserialized.channelSettings.length, equals(4));
        
        // Verify each channel was preserved correctly
        final generalChannel = deserialized.channelSettings[NotificationChannelKey.general];
        expect(generalChannel?.enabled, isTrue);
        expect(generalChannel?.customSound, isTrue);
        expect(generalChannel?.quietHoursStart, equals(22));
        expect(generalChannel?.quietHoursEnd, equals(6));
        
        final prayerChannel = deserialized.channelSettings[NotificationChannelKey.prayerTimes];
        expect(prayerChannel?.enabled, isFalse);
        expect(prayerChannel?.customVibration, isTrue);
        expect(prayerChannel?.quietHoursStart, equals(23));
        expect(prayerChannel?.quietHoursEnd, equals(5));
      });
    });

    group('Advanced Features Integration', () {
      test('should integrate advanced features with basic settings', () async {
        // Arrange - Create settings with advanced features
        final advancedSettings = NotificationSettings(
          globallyEnabled: true,
          maxNotifications: 20, // Low limit for bundling
          groupNotifications: true,
          enableAnalytics: true,
          advancedFeatures: AdvancedNotificationFeatures(
            smartGrouping: true,
            bundling: true,
            maxBundleSize: 5,
            scheduleOptimization: true,
            batteryOptimization: true,
            adaptiveTiming: false,
            priorityLearning: true,
            locationBased: false,
          ),
        );

        // Act - Test integration behavior
        final features = advancedSettings.advancedFeatures;

        // Assert - Verify advanced features integration
        expect(features.smartGrouping, isTrue);
        expect(features.bundling, isTrue);
        expect(features.maxBundleSize, equals(5));
        expect(features.scheduleOptimization, isTrue);
        expect(features.batteryOptimization, isTrue);
        expect(features.priorityLearning, isTrue);
        
        // Verify integration with basic settings
        expect(advancedSettings.maxNotifications, equals(20));
        expect(advancedSettings.groupNotifications, isTrue);
        expect(advancedSettings.enableAnalytics, isTrue);
      });

      test('should handle advanced features serialization', () async {
        // Arrange - Create settings with all advanced features
        final fullAdvancedSettings = NotificationSettings(
          globallyEnabled: true,
          advancedFeatures: AdvancedNotificationFeatures(
            smartGrouping: false,
            scheduleOptimization: true,
            batteryOptimization: false,
            adaptiveTiming: true,
            priorityLearning: false,
            locationBased: true,
            bundling: false,
            maxBundleSize: 10,
          ),
        );

        // Act - Serialize and deserialize
        final json = fullAdvancedSettings.toJson();
        final deserialized = NotificationSettings.fromJson(json);

        // Assert - Verify advanced features preserved
        final originalFeatures = fullAdvancedSettings.advancedFeatures;
        final deserializedFeatures = deserialized.advancedFeatures;
        
        expect(deserializedFeatures.smartGrouping, equals(originalFeatures.smartGrouping));
        expect(deserializedFeatures.scheduleOptimization, equals(originalFeatures.scheduleOptimization));
        expect(deserializedFeatures.batteryOptimization, equals(originalFeatures.batteryOptimization));
        expect(deserializedFeatures.adaptiveTiming, equals(originalFeatures.adaptiveTiming));
        expect(deserializedFeatures.priorityLearning, equals(originalFeatures.priorityLearning));
        expect(deserializedFeatures.locationBased, equals(originalFeatures.locationBased));
        expect(deserializedFeatures.bundling, equals(originalFeatures.bundling));
        expect(deserializedFeatures.maxBundleSize, equals(originalFeatures.maxBundleSize));
      });
    });

    group('Performance Integration', () {
      test('should handle large channel configurations efficiently', () async {
        // Arrange - Create settings with many channels
        final largeChannelMap = <NotificationChannelKey, NotificationChannelSettings>{};
        for (final channelKey in NotificationChannelKey.values) {
          largeChannelMap[channelKey] = NotificationChannelSettings(
            enabled: channelKey.index % 2 == 0, // Alternate enabled/disabled
            customSound: channelKey.index % 3 == 0,
            customVibration: channelKey.index % 4 == 0,
            customLights: channelKey.index % 5 == 0,
            customBadge: channelKey.index % 6 == 0,
            quietHoursStart: 22 + (channelKey.index % 3),
            quietHoursEnd: 6 + (channelKey.index % 3),
          );
        }

        final largeSettings = NotificationSettings(
          globallyEnabled: true,
          channelSettings: largeChannelMap,
        );

        // Act - Test performance with large configuration
        final startTime = DateTime.now();
        
        // Perform multiple operations
        for (int i = 0; i < 100; i++) {
          final json = largeSettings.toJson();
          final deserialized = NotificationSettings.fromJson(json);
          
          // Test channel operations
          for (final channelKey in NotificationChannelKey.values) {
            deserialized.isChannelEnabled(channelKey);
            deserialized.getEffectiveSoundSetting(channelKey);
            deserialized.getEffectiveVibrationSetting(channelKey);
          }
        }
        
        final endTime = DateTime.now();
        final duration = endTime.difference(startTime);

        // Assert - Should complete efficiently
        expect(duration.inMilliseconds, lessThan(5000)); // Should complete in under 5 seconds
        expect(largeSettings.channelSettings.length, equals(NotificationChannelKey.values.length));
      });

      test('should handle memory efficiently with complex settings', () async {
        // Arrange - Create many complex settings instances
        final settingsList = <NotificationSettings>[];

        // Act - Create many instances
        for (int i = 0; i < 1000; i++) {
          final settings = NotificationSettings(
            globallyEnabled: i % 2 == 0,
            channelSettings: complexChannelSettings,
            customSoundPath: 'sound_$i.mp3',
            customVibrationPattern: List.generate(i % 10 + 1, (index) => index * 100),
            maxNotifications: i % 100 + 1,
            historyRetentionDays: i % 30 + 1,
            advancedFeatures: AdvancedNotificationFeatures(
              maxBundleSize: i % 10 + 1,
              smartGrouping: i % 2 == 0,
              bundling: i % 3 == 0,
            ),
          );
          settingsList.add(settings);
        }

        // Assert - Should handle memory efficiently
        expect(settingsList.length, equals(1000));
        
        // Verify instances are properly created
        expect(settingsList.first.maxNotifications, equals(1));
        expect(settingsList.last.maxNotifications, equals(100));
      });
    });
  });
}
