import 'package:flutter_riverpod/flutter_riverpod.dart';

// Context7 MCP: Updated to use unified notification settings provider
import '../notifications/providers/unified_notification_provider.dart';
import '../services/interfaces/prayer_times_service_interface.dart';
import '../utils/logger.dart';
import 'notification_service.dart';

/// Simple prayer notification scheduler following Context7 MCP best practices
class PrayerNotificationScheduler {
  /// Singleton instance
  static final PrayerNotificationScheduler _instance = PrayerNotificationScheduler._internal();

  /// Factory constructor
  factory PrayerNotificationScheduler() => _instance;

  /// Private constructor
  PrayerNotificationScheduler._internal();

  /// Notification service instance
  final NotificationService _notificationService = NotificationService();

  /// Schedule prayer time notifications based on settings
  Future<void> schedulePrayerNotifications({
    required Map<String, DateTime> prayerTimes,
    required NotificationSettings settings,
    String languageCode = 'en',
  }) async {
    try {
      AppLogger.debug('PrayerNotificationScheduler: Starting to schedule notifications');

      // Cancel all existing notifications first
      await _notificationService.cancelAllNotifications();

      // Only schedule if notifications are enabled
      if (!settings.notificationsEnabled) {
        AppLogger.debug('PrayerNotificationScheduler: Notifications disabled, skipping');
        return;
      }

      // Get prayer times for today
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      // Schedule notifications for each enabled prayer
      int notificationId = 1;

      // Fajr
      if (settings.prayerNotificationsEnabled['Fajr'] == true && prayerTimes['Fajr'] != null) {
        await _scheduleNotificationForPrayer(
          id: notificationId++,
          prayerName: 'Fajr',
          prayerTime: prayerTimes['Fajr']!,
          settings: settings,
          languageCode: languageCode,
        );
      }

      // Dhuhr
      if (settings.prayerNotificationsEnabled['Dhuhr'] == true && prayerTimes['Dhuhr'] != null) {
        await _scheduleNotificationForPrayer(
          id: notificationId++,
          prayerName: 'Dhuhr',
          prayerTime: prayerTimes['Dhuhr']!,
          settings: settings,
          languageCode: languageCode,
        );
      }

      // Asr
      if (settings.prayerNotificationsEnabled['Asr'] == true && prayerTimes['Asr'] != null) {
        await _scheduleNotificationForPrayer(
          id: notificationId++,
          prayerName: 'Asr',
          prayerTime: prayerTimes['Asr']!,
          settings: settings,
          languageCode: languageCode,
        );
      }

      // Maghrib
      if (settings.prayerNotificationsEnabled['Maghrib'] == true && prayerTimes['Maghrib'] != null) {
        await _scheduleNotificationForPrayer(
          id: notificationId++,
          prayerName: 'Maghrib',
          prayerTime: prayerTimes['Maghrib']!,
          settings: settings,
          languageCode: languageCode,
        );
      }

      // Isha
      if (settings.prayerNotificationsEnabled['Isha'] == true && prayerTimes['Isha'] != null) {
        await _scheduleNotificationForPrayer(
          id: notificationId++,
          prayerName: 'Isha',
          prayerTime: prayerTimes['Isha']!,
          settings: settings,
          languageCode: languageCode,
        );
      }

      AppLogger.debug('PrayerNotificationScheduler: Scheduled ${notificationId - 1} notifications');
    } catch (e) {
      AppLogger.error('PrayerNotificationScheduler: Failed to schedule notifications - $e');
    }
  }

  /// Schedule a notification for a specific prayer
  Future<void> _scheduleNotificationForPrayer({
    required int id,
    required String prayerName,
    required DateTime prayerTime,
    required NotificationSettings settings,
    required String languageCode,
  }) async {
    try {
      // Calculate notification time based on settings
      DateTime notificationTime;

      if (settings.notificationTiming == NotificationTiming.exactTime) {
        notificationTime = prayerTime;
      } else {
        // Before prayer time
        notificationTime = prayerTime.subtract(Duration(minutes: settings.minutesBefore));
      }

      // Only schedule if the notification time is in the future
      if (notificationTime.isAfter(DateTime.now())) {
        // Create notification title and body based on language
        final title = _getNotificationTitle(prayerName, languageCode);
        final body = _getNotificationBody(prayerName, settings, languageCode);

        await _notificationService.scheduleNotification(
          id: id,
          title: title,
          body: body,
          scheduledDate: notificationTime,
          payload: 'prayer_$prayerName',
        );

        AppLogger.debug('PrayerNotificationScheduler: Scheduled $prayerName notification for $notificationTime');
      } else {
        AppLogger.debug('PrayerNotificationScheduler: Skipped $prayerName notification (time passed)');
      }
    } catch (e) {
      AppLogger.error('PrayerNotificationScheduler: Failed to schedule $prayerName notification - $e');
    }
  }

  /// Get notification title based on language
  String _getNotificationTitle(String prayerName, String languageCode) {
    if (languageCode == 'ar') {
      switch (prayerName) {
        case 'Fajr':
          return 'صلاة الفجر';
        case 'Dhuhr':
          return 'صلاة الظهر';
        case 'Asr':
          return 'صلاة العصر';
        case 'Maghrib':
          return 'صلاة المغرب';
        case 'Isha':
          return 'صلاة العشاء';
        default:
          return 'وقت الصلاة';
      }
    } else {
      return '$prayerName Prayer Time';
    }
  }

  /// Get notification body based on language and settings
  String _getNotificationBody(String prayerName, NotificationSettings settings, String languageCode) {
    if (languageCode == 'ar') {
      if (settings.notificationTiming == NotificationTiming.exactTime) {
        return 'حان الآن وقت صلاة $prayerName';
      } else {
        return 'سيحين وقت صلاة $prayerName خلال ${settings.minutesBefore} دقيقة';
      }
    } else {
      if (settings.notificationTiming == NotificationTiming.exactTime) {
        return 'It\'s time for $prayerName prayer';
      } else {
        return '$prayerName prayer time is in ${settings.minutesBefore} minutes';
      }
    }
  }

  /// Cancel all scheduled notifications
  Future<void> cancelAllNotifications() async {
    try {
      await _notificationService.cancelAllNotifications();
      AppLogger.debug('PrayerNotificationScheduler: Cancelled all notifications');
    } catch (e) {
      AppLogger.error('PrayerNotificationScheduler: Failed to cancel notifications - $e');
    }
  }

  /// Get pending notifications count
  Future<int> getPendingNotificationsCount() async {
    try {
      final pending = await _notificationService.getPendingNotifications();
      return pending.length;
    } catch (e) {
      AppLogger.error('PrayerNotificationScheduler: Failed to get pending count - $e');
      return 0;
    }
  }
}
