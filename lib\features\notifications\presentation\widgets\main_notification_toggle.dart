import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../core/utils/l10n.dart';
import '../../../../core/notifications/providers/unified_notification_provider.dart';
import '../../domain/services/notification_manager.dart';

/// Main notification toggle widget
class MainNotificationToggle extends ConsumerWidget {
  /// Constructor
  const MainNotificationToggle({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Get notification settings from unified provider
    final notificationSettingsAsync = ref.watch(unifiedNotificationSettingsProvider);

    return notificationSettingsAsync.when(
      loading: () => const DecoratedBox(
        decoration: BoxDecoration(color: Colors.grey, borderRadius: BorderRadius.all(Radius.circular(12))),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Center(child: CircularProgressIndicator()),
        ),
      ),
      error: (error, stack) => DecoratedBox(
        decoration: const BoxDecoration(color: Colors.red, borderRadius: BorderRadius.all(Radius.circular(12))),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Text('Error: $error', style: const TextStyle(color: Colors.white)),
        ),
      ),
      data: (notificationSettings) => DecoratedBox(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Colors.blue[50]!, Colors.blue[100]!], // Match sidebar gradient
          ),
          borderRadius: BorderRadius.circular(8), // Match sidebar border radius
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(15), // Match sidebar shadow
              blurRadius: 3,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(12.0), // Reduced from 16.0
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.notifications_active,
                        color: notificationSettings.generalSettings.globallyEnabled
                            ? Colors.blue[600]! // Match sidebar icon color
                            : AppColors.inactive,
                        size: 24,
                      ),
                      const SizedBox(width: 12),
                      Text(
                        context.l10n?.enableNotifications ?? 'Enable Notifications',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppColors.textPrimaryLight, // Match sidebar text color
                        ),
                      ),
                    ],
                  ),
                  Switch(
                    value: notificationSettings.generalSettings.globallyEnabled,
                    onChanged: (value) async {
                      if (value) {
                        // Request permissions first when enabling notifications
                        final notificationManager = ref.read(notificationManagerProvider);
                        final permissionsGranted = await notificationManager.requestPermissions();

                        if (permissionsGranted) {
                          // Enable notifications and schedule them
                          await ref.read(unifiedNotificationSettingsProvider.notifier).toggleGlobalNotifications();
                          await notificationManager.schedulePrayerTimeNotifications();
                        } else {
                          // Show a message that permissions are required
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  context.l10n?.notificationPermissionRequired ??
                                      'Notification permissions are required to enable notifications',
                                ),
                                duration: const Duration(seconds: 3),
                              ),
                            );
                          }
                        }
                      } else {
                        // Disable notifications and cancel all
                        await ref.read(unifiedNotificationSettingsProvider.notifier).toggleGlobalNotifications();
                        await ref.read(notificationManagerProvider).cancelAllNotifications();
                      }
                    },
                    activeThumbColor: Colors.blue[600]!, // Match sidebar toggle color
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
