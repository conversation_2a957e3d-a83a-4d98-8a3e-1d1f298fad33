import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../utils/app_logger.dart';

part 'migration_feature_flags.freezed.dart';
part 'migration_feature_flags.g.dart';

/// Migration Feature Flags State following Context7 MCP best practices
///
/// This state manages feature flags that control the gradual rollout of new
/// domain-specific providers while maintaining backward compatibility with
/// the legacy monolithic AppSettingsProvider.
///
/// **Key Features:**
/// - ✅ **Gradual Rollout**: Enable new providers one domain at a time
/// - ✅ **Safe Fallback**: Automatic fallback to legacy system on errors
/// - ✅ **Runtime Configuration**: Flags can be updated without app restart
/// - ✅ **Persistent Storage**: Flags persist across app sessions
/// - ✅ **Validation**: Built-in validation for flag combinations
/// - ✅ **Monitoring**: Comprehensive logging for migration tracking
///
/// **Usage:**
/// ```dart
/// // Check if theme provider should be used
/// final flags = await ref.read(migrationFeatureFlagsProvider.future);
/// if (flags.useThemeProvider) {
///   // Use new theme provider
/// } else {
///   // Use legacy implementation
/// }
/// ```
@freezed
abstract class MigrationFeatureFlags with _$MigrationFeatureFlags {
  const MigrationFeatureFlags._();

  /// Creates migration feature flags with the specified configuration
  ///
  /// **Context7 MCP: Notification Provider Consolidation Complete**
  /// The notification provider consolidation has been completed successfully.
  /// The useNotificationProvider flag is now permanently enabled and should
  /// be considered for removal in future cleanup phases.
  const factory MigrationFeatureFlags({
    /// Enable new theme settings provider
    @Default(true) bool useThemeProvider,

    /// Enable new notification settings provider
    /// **CONSOLIDATION COMPLETE**: This flag is permanently enabled
    /// as the notification provider consolidation is stable and complete.
    /// Consider removing in cleanup phase.
    @Default(true) bool useNotificationProvider,

    /// Enable new prayer times settings provider
    @Default(true) bool usePrayerTimesProvider,

    /// Enable new location settings provider
    @Default(true) bool useLocationProvider,

    /// Enable new performance settings provider
    @Default(true) bool usePerformanceProvider,

    /// Enable new advanced settings provider
    @Default(true) bool useAdvancedProvider,

    /// Master flag to enable all new providers
    @Default(true) bool enableAllNewProviders,

    /// Disable legacy fallback (only when all providers are stable)
    /// **NOTIFICATION CONSOLIDATION**: Legacy notification fallback can be
    /// safely disabled as the unified notification system is stable.
    @Default(false) bool disableLegacyFallback,

    /// Enable migration validation and monitoring
    @Default(true) bool enableMigrationValidation,

    /// Migration phase (0=legacy, 1=partial, 2=full, 3=cleanup)
    /// **NOTIFICATION PHASE**: Notification consolidation is in cleanup phase (3)
    @Default(3) int migrationPhase,

    /// Last update timestamp
    @Default('') String lastUpdated,
  }) = _MigrationFeatureFlags;

  /// Create MigrationFeatureFlags from JSON
  factory MigrationFeatureFlags.fromJson(Map<String, dynamic> json) => _$MigrationFeatureFlagsFromJson(json);

  /// Check if any new providers are enabled
  bool get hasAnyNewProvidersEnabled =>
      useThemeProvider ||
      useNotificationProvider ||
      usePrayerTimesProvider ||
      useLocationProvider ||
      usePerformanceProvider ||
      useAdvancedProvider;

  /// Check if all new providers are enabled
  bool get hasAllNewProvidersEnabled =>
      useThemeProvider &&
      useNotificationProvider &&
      usePrayerTimesProvider &&
      useLocationProvider &&
      usePerformanceProvider &&
      useAdvancedProvider;

  /// Get migration progress percentage (0-100)
  int get migrationProgressPercentage {
    final enabledCount = [
      useThemeProvider,
      useNotificationProvider,
      usePrayerTimesProvider,
      useLocationProvider,
      usePerformanceProvider,
      useAdvancedProvider,
    ].where((enabled) => enabled).length;

    return (enabledCount / 6 * 100).round();
  }

  /// Get list of enabled providers
  List<String> get enabledProviders {
    final enabled = <String>[];
    if (useThemeProvider) enabled.add('theme');
    if (useNotificationProvider) enabled.add('notification');
    if (usePrayerTimesProvider) enabled.add('prayerTimes');
    if (useLocationProvider) enabled.add('location');
    if (usePerformanceProvider) enabled.add('performance');
    if (useAdvancedProvider) enabled.add('advanced');
    return enabled;
  }

  /// Get list of disabled providers
  List<String> get disabledProviders {
    final disabled = <String>[];
    if (!useThemeProvider) disabled.add('theme');
    if (!useNotificationProvider) disabled.add('notification');
    if (!usePrayerTimesProvider) disabled.add('prayerTimes');
    if (!useLocationProvider) disabled.add('location');
    if (!usePerformanceProvider) disabled.add('performance');
    if (!useAdvancedProvider) disabled.add('advanced');
    return disabled;
  }

  /// Check if migration is safe to proceed
  bool get isMigrationSafe => enableMigrationValidation && !disableLegacyFallback;

  /// Get migration phase description
  String get migrationPhaseDescription {
    switch (migrationPhase) {
      case 0:
        return 'Legacy - Using monolithic AppSettingsProvider';
      case 1:
        return 'Partial - Some domain providers enabled';
      case 2:
        return 'Full - All domain providers enabled';
      case 3:
        return 'Cleanup - Legacy system being removed';
      default:
        return 'Unknown migration phase';
    }
  }

  /// Create flags for development environment (all enabled)
  /// Context7 MCP: Notification consolidation complete - phase 3 (cleanup)
  factory MigrationFeatureFlags.development() {
    return MigrationFeatureFlags(
      useThemeProvider: true,
      useNotificationProvider: true, // Consolidation complete
      usePrayerTimesProvider: true,
      useLocationProvider: true,
      usePerformanceProvider: true,
      useAdvancedProvider: true,
      enableAllNewProviders: true,
      disableLegacyFallback: false, // Keep fallback for safety
      enableMigrationValidation: true,
      migrationPhase: 3, // Cleanup phase for notification consolidation
      lastUpdated: DateTime.now().toIso8601String(),
    );
  }

  /// Create flags for production environment (gradual rollout)
  factory MigrationFeatureFlags.production() {
    return const MigrationFeatureFlags(
      useThemeProvider: false,
      useNotificationProvider: false,
      usePrayerTimesProvider: false,
      useLocationProvider: false,
      usePerformanceProvider: false,
      useAdvancedProvider: false,
      enableAllNewProviders: false,
      disableLegacyFallback: false,
      enableMigrationValidation: true,
      migrationPhase: 0,
    );
  }

  /// Create flags for testing environment (selective enabling)
  factory MigrationFeatureFlags.testing({bool enableTheme = true, bool enableNotifications = false}) {
    return MigrationFeatureFlags(
      useThemeProvider: enableTheme,
      useNotificationProvider: enableNotifications,
      usePrayerTimesProvider: false,
      useLocationProvider: false,
      usePerformanceProvider: false,
      useAdvancedProvider: false,
      enableAllNewProviders: false,
      disableLegacyFallback: false,
      enableMigrationValidation: true,
      migrationPhase: 1,
      lastUpdated: DateTime.now().toIso8601String(),
    );
  }

  /// Enable specific provider
  MigrationFeatureFlags enableProvider(String providerName) {
    switch (providerName.toLowerCase()) {
      case 'theme':
        return copyWith(useThemeProvider: true, lastUpdated: DateTime.now().toIso8601String());
      case 'notification':
        return copyWith(useNotificationProvider: true, lastUpdated: DateTime.now().toIso8601String());
      case 'prayertimes':
        return copyWith(usePrayerTimesProvider: true, lastUpdated: DateTime.now().toIso8601String());
      case 'location':
        return copyWith(useLocationProvider: true, lastUpdated: DateTime.now().toIso8601String());
      case 'performance':
        return copyWith(usePerformanceProvider: true, lastUpdated: DateTime.now().toIso8601String());
      case 'advanced':
        return copyWith(useAdvancedProvider: true, lastUpdated: DateTime.now().toIso8601String());
      default:
        return this;
    }
  }

  /// Disable specific provider
  MigrationFeatureFlags disableProvider(String providerName) {
    switch (providerName.toLowerCase()) {
      case 'theme':
        return copyWith(useThemeProvider: false, lastUpdated: DateTime.now().toIso8601String());
      case 'notification':
        return copyWith(useNotificationProvider: false, lastUpdated: DateTime.now().toIso8601String());
      case 'prayertimes':
        return copyWith(usePrayerTimesProvider: false, lastUpdated: DateTime.now().toIso8601String());
      case 'location':
        return copyWith(useLocationProvider: false, lastUpdated: DateTime.now().toIso8601String());
      case 'performance':
        return copyWith(usePerformanceProvider: false, lastUpdated: DateTime.now().toIso8601String());
      case 'advanced':
        return copyWith(useAdvancedProvider: false, lastUpdated: DateTime.now().toIso8601String());
      default:
        return this;
    }
  }

  /// Enable all providers
  MigrationFeatureFlags enableAllProviders() {
    return copyWith(
      useThemeProvider: true,
      useNotificationProvider: true,
      usePrayerTimesProvider: true,
      useLocationProvider: true,
      usePerformanceProvider: true,
      useAdvancedProvider: true,
      enableAllNewProviders: true,
      migrationPhase: 2,
      lastUpdated: DateTime.now().toIso8601String(),
    );
  }

  /// Disable all providers (revert to legacy)
  MigrationFeatureFlags disableAllProviders() {
    return copyWith(
      useThemeProvider: false,
      useNotificationProvider: false,
      usePrayerTimesProvider: false,
      useLocationProvider: false,
      usePerformanceProvider: false,
      useAdvancedProvider: false,
      enableAllNewProviders: false,
      disableLegacyFallback: false,
      migrationPhase: 0,
      lastUpdated: DateTime.now().toIso8601String(),
    );
  }
}

/// Migration Feature Flags Provider following Context7 MCP best practices
///
/// This provider manages the feature flags that control the gradual migration
/// from monolithic AppSettingsProvider to domain-specific providers.
@riverpod
class MigrationFeatureFlagsNotifier extends _$MigrationFeatureFlagsNotifier {
  /// Initialize migration feature flags
  @override
  Future<MigrationFeatureFlags> build() async {
    try {
      AppLogger.debug('MigrationFeatureFlags: Initializing feature flags');

      // Create default flags based on environment
      final defaultFlags = _createDefaultFlags();

      AppLogger.debug('MigrationFeatureFlags: Created default flags for environment');
      return defaultFlags;
    } on Exception catch (e, stackTrace) {
      AppLogger.error('MigrationFeatureFlags: Error initializing flags', e, stackTrace);

      // Return safe defaults on error
      return MigrationFeatureFlags.production();
    }
  }

  /// Enable specific provider
  Future<void> enableProvider(String providerName) async {
    try {
      final currentFlags = await future;
      final updatedFlags = currentFlags.enableProvider(providerName);

      // Update state
      state = AsyncData(updatedFlags);

      AppLogger.info('MigrationFeatureFlags: Enabled provider: $providerName');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('MigrationFeatureFlags: Error enabling provider', e, stackTrace);
      rethrow;
    }
  }

  /// Disable specific provider
  Future<void> disableProvider(String providerName) async {
    try {
      final currentFlags = await future;
      final updatedFlags = currentFlags.disableProvider(providerName);

      // Update state
      state = AsyncData(updatedFlags);

      AppLogger.info('MigrationFeatureFlags: Disabled provider: $providerName');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('MigrationFeatureFlags: Error disabling provider', e, stackTrace);
      rethrow;
    }
  }

  /// Enable all providers
  Future<void> enableAllProviders() async {
    try {
      final currentFlags = await future;
      final updatedFlags = currentFlags.enableAllProviders();

      // Update state
      state = AsyncData(updatedFlags);

      AppLogger.info('MigrationFeatureFlags: Enabled all providers - migration complete');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('MigrationFeatureFlags: Error enabling all providers', e, stackTrace);
      rethrow;
    }
  }

  /// Disable all providers (revert to legacy)
  Future<void> disableAllProviders() async {
    try {
      final currentFlags = await future;
      final updatedFlags = currentFlags.disableAllProviders();

      // Update state
      state = AsyncData(updatedFlags);

      AppLogger.info('MigrationFeatureFlags: Disabled all providers - reverted to legacy');
    } on Exception catch (e, stackTrace) {
      AppLogger.error('MigrationFeatureFlags: Error disabling all providers', e, stackTrace);
      rethrow;
    }
  }

  /// Create default flags based on environment
  MigrationFeatureFlags _createDefaultFlags() {
    // Check environment variables and configuration for gradual rollout
    return _getGradualRolloutFlags();
  }

  /// Get gradual rollout flags based on environment and configuration
  MigrationFeatureFlags _getGradualRolloutFlags() {
    // Check for environment-specific rollout configuration
    const environment = String.fromEnvironment('FLUTTER_ENV', defaultValue: 'production');

    switch (environment.toLowerCase()) {
      case 'development':
        return MigrationFeatureFlags.development();
      case 'staging':
        return _getStagingRolloutFlags();
      case 'testing':
        return MigrationFeatureFlags.testing();
      case 'production':
      default:
        return _getProductionRolloutFlags();
    }
  }

  /// Get staging environment rollout flags (partial rollout for testing)
  /// Context7 MCP: Notification consolidation complete and stable
  MigrationFeatureFlags _getStagingRolloutFlags() {
    return MigrationFeatureFlags(
      // Enable stable providers first
      useThemeProvider: true,
      useNotificationProvider: true, // Consolidation complete and stable
      usePrayerTimesProvider: true,

      // Keep newer providers disabled for gradual testing
      useLocationProvider: false,
      usePerformanceProvider: false,
      useAdvancedProvider: false,

      enableAllNewProviders: false,
      disableLegacyFallback: false, // Keep fallback for safety
      enableMigrationValidation: true,
      migrationPhase: 2, // Full migration for notification provider
      lastUpdated: DateTime.now().toIso8601String(),
    );
  }

  /// Get production environment rollout flags (conservative rollout)
  MigrationFeatureFlags _getProductionRolloutFlags() {
    // Check for gradual rollout percentage (0-100)
    const rolloutPercentage = int.fromEnvironment('MIGRATION_ROLLOUT_PERCENTAGE', defaultValue: 0);

    // Check for specific provider rollout flags
    const enableTheme = bool.fromEnvironment('ENABLE_THEME_PROVIDER', defaultValue: false);
    const enableNotifications = bool.fromEnvironment('ENABLE_NOTIFICATION_PROVIDER', defaultValue: false);
    const enablePrayerTimes = bool.fromEnvironment('ENABLE_PRAYER_TIMES_PROVIDER', defaultValue: false);
    const enableLocation = bool.fromEnvironment('ENABLE_LOCATION_PROVIDER', defaultValue: false);
    const enablePerformance = bool.fromEnvironment('ENABLE_PERFORMANCE_PROVIDER', defaultValue: false);
    const enableAdvanced = bool.fromEnvironment('ENABLE_ADVANCED_PROVIDER', defaultValue: false);

    // Determine rollout based on percentage and specific flags
    if (rolloutPercentage >= 100) {
      // Full rollout
      return MigrationFeatureFlags.development().copyWith(
        disableLegacyFallback: false, // Keep fallback for production safety
        migrationPhase: 2,
      );
    } else if (rolloutPercentage >= 50) {
      // Partial rollout - enable stable providers
      // Context7 MCP: Notification provider is always enabled (consolidation complete)
      return MigrationFeatureFlags(
        useThemeProvider: enableTheme || true,
        useNotificationProvider: true, // Always enabled - consolidation complete
        usePrayerTimesProvider: enablePrayerTimes || true,
        useLocationProvider: enableLocation || false,
        usePerformanceProvider: enablePerformance || false,
        useAdvancedProvider: enableAdvanced || false,
        enableAllNewProviders: false,
        disableLegacyFallback: false,
        enableMigrationValidation: true,
        migrationPhase: 1,
        lastUpdated: DateTime.now().toIso8601String(),
      );
    } else if (rolloutPercentage > 0) {
      // Limited rollout - theme and notification providers (notification consolidation complete)
      // Context7 MCP: Notification provider is always enabled (consolidation complete)
      return MigrationFeatureFlags(
        useThemeProvider: enableTheme || true,
        useNotificationProvider: true, // Always enabled - consolidation complete
        usePrayerTimesProvider: enablePrayerTimes || false,
        useLocationProvider: enableLocation || false,
        usePerformanceProvider: enablePerformance || false,
        useAdvancedProvider: enableAdvanced || false,
        enableAllNewProviders: false,
        disableLegacyFallback: false,
        enableMigrationValidation: true,
        migrationPhase: 1, // Updated to reflect notification consolidation completion
        lastUpdated: DateTime.now().toIso8601String(),
      );
    } else {
      // No rollout - use legacy system with specific overrides
      // Context7 MCP: Notification provider is always enabled (consolidation complete)
      return MigrationFeatureFlags(
        useThemeProvider: enableTheme,
        useNotificationProvider: enableNotifications || true, // Always enabled - consolidation complete
        usePrayerTimesProvider: enablePrayerTimes,
        useLocationProvider: enableLocation,
        usePerformanceProvider: enablePerformance,
        useAdvancedProvider: enableAdvanced,
        enableAllNewProviders: false,
        disableLegacyFallback: false,
        enableMigrationValidation: true,
        migrationPhase: 0,
        lastUpdated: DateTime.now().toIso8601String(),
      );
    }
  }
}
