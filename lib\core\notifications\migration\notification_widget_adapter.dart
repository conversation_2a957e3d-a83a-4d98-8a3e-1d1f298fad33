import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'main_app_adapter.dart';
import 'prayer_notification_adapter.dart';
import 'service_registry_adapter.dart';
import '../../logging/app_logger.dart';

/// Notification Widget Adapter for Phase 3 Migration
///
/// **Context7 MCP Implementation:**
/// - Provides unified widget interfaces for notification components
/// - Maintains backward compatibility with existing widget APIs
/// - <PERSON>les graceful fallback for widget rendering
/// - Implements comprehensive error boundaries
/// - Supports progressive enhancement of widget features
/// - Provides consistent theming and styling
///
/// **Widget Migration Strategy:**
/// - Wrap legacy notification widgets with adapter layer
/// - Provide unified data access through adapter providers
/// - Maintain existing widget APIs for backward compatibility
/// - Add enhanced features through unified provider system
/// - Support emergency fallback to legacy widget behavior

/// Notification Settings Widget Adapter
class NotificationSettingsWidgetAdapter extends ConsumerWidget {
  final Widget? child;
  final bool showAdvancedSettings;
  final VoidCallback? onSettingsChanged;

  const NotificationSettingsWidgetAdapter({
    super.key,
    this.child,
    this.showAdvancedSettings = false,
    this.onSettingsChanged,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(mainAppNotificationSettingsProvider).when(
      data: (settings) => _buildSettingsWidget(context, ref, settings),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => _buildErrorWidget(context, error),
    );
  }

  Widget _buildSettingsWidget(
    BuildContext context,
    WidgetRef ref,
    MainAppNotificationSettings settings,
  ) {
    return Column(
      children: [
        // Global notification toggle
        SwitchListTile(
          title: const Text('Enable Notifications'),
          subtitle: Text('Source: ${settings.source}'),
          value: settings.globallyEnabled,
          onChanged: (value) => _updateGlobalSettings(ref, value),
        ),
        
        // Prayer notifications section
        if (settings.globallyEnabled) ...[
          const Divider(),
          _buildPrayerNotificationsSection(context, ref, settings),
        ],
        
        // Advanced settings
        if (showAdvancedSettings && settings.globallyEnabled) ...[
          const Divider(),
          _buildAdvancedSettingsSection(context, ref, settings),
        ],
        
        // Custom child widget
        if (child != null) child!,
      ],
    );
  }

  Widget _buildPrayerNotificationsSection(
    BuildContext context,
    WidgetRef ref,
    MainAppNotificationSettings settings,
  ) {
    return ref.watch(prayerNotificationAdapterProvider).when(
      data: (adapter) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SwitchListTile(
            title: const Text('Prayer Notifications'),
            subtitle: Text('Health: ${adapter.health.healthSummary}'),
            value: settings.prayerNotificationsEnabled,
            onChanged: (value) => _updatePrayerSettings(ref, value),
          ),
          
          // Individual prayer settings
          if (settings.prayerNotificationsEnabled) ...[
            Padding(
              padding: const EdgeInsets.only(left: 16.0),
              child: Column(
                children: adapter.enabledPrayers.map((prayer) => 
                  CheckboxListTile(
                    title: Text(prayer),
                    value: adapter.isPrayerEnabled(prayer),
                    onChanged: (value) => _updateIndividualPrayer(ref, prayer, value ?? false),
                  ),
                ).toList(),
              ),
            ),
          ],
        ],
      ),
      loading: () => const ListTile(
        title: Text('Prayer Notifications'),
        trailing: CircularProgressIndicator(),
      ),
      error: (error, stackTrace) => ListTile(
        title: const Text('Prayer Notifications'),
        subtitle: Text('Error: $error'),
        trailing: const Icon(Icons.error, color: Colors.red),
      ),
    );
  }

  Widget _buildAdvancedSettingsSection(
    BuildContext context,
    WidgetRef ref,
    MainAppNotificationSettings settings,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const ListTile(
          title: Text('Advanced Settings'),
          subtitle: Text('Sound, vibration, and timing options'),
        ),
        
        SwitchListTile(
          title: const Text('Sound'),
          value: settings.soundEnabled,
          onChanged: (value) => _updateSoundSettings(ref, value),
        ),
        
        SwitchListTile(
          title: const Text('Vibration'),
          value: settings.vibrationEnabled,
          onChanged: (value) => _updateVibrationSettings(ref, value),
        ),
        
        // Service registry health indicator
        ref.watch(unifiedServiceRegistryProvider).when(
          data: (registry) => ListTile(
            title: const Text('System Health'),
            subtitle: Text(registry.health.healthSummary),
            trailing: Icon(
              registry.health.isHealthy ? Icons.check_circle : Icons.error,
              color: registry.health.isHealthy ? Colors.green : Colors.red,
            ),
          ),
          loading: () => const ListTile(
            title: Text('System Health'),
            trailing: CircularProgressIndicator(),
          ),
          error: (error, stackTrace) => ListTile(
            title: const Text('System Health'),
            subtitle: const Text('Error checking health'),
            trailing: const Icon(Icons.error, color: Colors.red),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorWidget(BuildContext context, Object error) {
    AppLogger.error(
      'NotificationSettingsWidgetAdapter: Error building widget',
      context: {'error': error.toString()},
    );

    return Card(
      color: Theme.of(context).colorScheme.errorContainer,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).colorScheme.error,
              size: 48,
            ),
            const SizedBox(height: 8),
            Text(
              'Notification Settings Unavailable',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Using emergency fallback mode',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  void _updateGlobalSettings(WidgetRef ref, bool enabled) {
    try {
      // Update through main app adapter
      ref.read(mainAppNotificationManagerProvider).then((manager) {
        if (enabled) {
          manager.enableNotifications();
        } else {
          manager.disableNotifications();
        }
        onSettingsChanged?.call();
      });
    } catch (e) {
      AppLogger.error(
        'NotificationSettingsWidgetAdapter: Failed to update global settings',
        context: {'enabled': enabled, 'error': e.toString()},
      );
    }
  }

  void _updatePrayerSettings(WidgetRef ref, bool enabled) {
    try {
      ref.read(prayerNotificationAdapterProvider).then((adapter) async {
        if (enabled) {
          // Enable prayer notifications with current location
          // This would typically get location from a location service
          await adapter.schedulePrayerNotifications(
            date: DateTime.now(),
            latitude: 26.0667, // Bahrain coordinates as default
            longitude: 50.5577,
          );
        } else {
          await adapter.cancelAllPrayerNotifications();
        }
        onSettingsChanged?.call();
      });
    } catch (e) {
      AppLogger.error(
        'NotificationSettingsWidgetAdapter: Failed to update prayer settings',
        context: {'enabled': enabled, 'error': e.toString()},
      );
    }
  }

  void _updateIndividualPrayer(WidgetRef ref, String prayer, bool enabled) {
    try {
      AppLogger.debug(
        'NotificationSettingsWidgetAdapter: Updating individual prayer setting',
        context: {'prayer': prayer, 'enabled': enabled},
      );
      
      // Individual prayer updates would be handled through the service registry
      ref.read(unifiedServiceRegistryProvider).then((registry) {
        final settingsService = registry.settingsService;
        // Update individual prayer setting through settings service
        // This would typically call a method like:
        // settingsService.updatePrayerSetting(prayer, enabled);
        onSettingsChanged?.call();
      });
    } catch (e) {
      AppLogger.error(
        'NotificationSettingsWidgetAdapter: Failed to update individual prayer',
        context: {'prayer': prayer, 'enabled': enabled, 'error': e.toString()},
      );
    }
  }

  void _updateSoundSettings(WidgetRef ref, bool enabled) {
    try {
      ref.read(unifiedServiceRegistryProvider).then((registry) {
        final settingsService = registry.settingsService;
        // Update sound settings through settings service
        onSettingsChanged?.call();
      });
    } catch (e) {
      AppLogger.error(
        'NotificationSettingsWidgetAdapter: Failed to update sound settings',
        context: {'enabled': enabled, 'error': e.toString()},
      );
    }
  }

  void _updateVibrationSettings(WidgetRef ref, bool enabled) {
    try {
      ref.read(unifiedServiceRegistryProvider).then((registry) {
        final settingsService = registry.settingsService;
        // Update vibration settings through settings service
        onSettingsChanged?.call();
      });
    } catch (e) {
      AppLogger.error(
        'NotificationSettingsWidgetAdapter: Failed to update vibration settings',
        context: {'enabled': enabled, 'error': e.toString()},
      );
    }
  }
}

/// Prayer Time Display Widget Adapter
class PrayerTimeDisplayWidgetAdapter extends ConsumerWidget {
  final DateTime date;
  final double? latitude;
  final double? longitude;

  const PrayerTimeDisplayWidgetAdapter({
    super.key,
    required this.date,
    this.latitude,
    this.longitude,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(prayerNotificationAdapterProvider).when(
      data: (adapter) => _buildPrayerTimeDisplay(context, adapter),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => _buildErrorDisplay(context, error),
    );
  }

  Widget _buildPrayerTimeDisplay(BuildContext context, PrayerNotificationAdapter adapter) {
    final prayerSettings = adapter.prayerSettings;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Prayer Times',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            
            Text(
              'Date: ${date.toLocal().toString().split(' ')[0]}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            
            if (prayerSettings.globallyEnabled && prayerSettings.prayerNotificationsEnabled) ...[
              const SizedBox(height: 8),
              Text(
                'Notifications: Enabled',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.green,
                ),
              ),
              
              Text(
                'Enabled Prayers: ${prayerSettings.enabledPrayers.join(', ')}',
                style: Theme.of(context).textTheme.bodySmall,
              ),
            ] else ...[
              const SizedBox(height: 8),
              Text(
                'Notifications: Disabled',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey,
                ),
              ),
            ],
            
            const SizedBox(height: 8),
            Text(
              'Provider: ${prayerSettings.isUsingUnifiedProvider ? 'Unified' : 'Legacy'}',
              style: Theme.of(context).textTheme.bodySmall,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorDisplay(BuildContext context, Object error) {
    return Card(
      color: Theme.of(context).colorScheme.errorContainer,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(
              Icons.error_outline,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 8),
            Text(
              'Prayer times unavailable',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
