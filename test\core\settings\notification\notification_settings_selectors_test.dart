// ignore_for_file: public_member_api_docs

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:riverpod/riverpod.dart';

// Context7 MCP: Updated to use unified notification settings provider
import '../../../../lib/core/notifications/providers/unified_notification_provider.dart';
import '../../../../lib/core/settings/notification/notification_settings_selectors.dart';
import '../../../../lib/core/settings/notification/notification_settings_state.dart';
import '../../../../lib/core/notifications/providers/unified_notification_provider.dart';

/// Notification Settings Selectors Tests
///
/// Comprehensive test suite for notification settings selectors following
/// Context7 MCP best practices for Riverpod testing.
///
/// **Test Coverage:**
/// - Core notification selectors (enabled, timing, sound, vibration)
/// - Prayer-specific selectors (individual prayers, preferences)
/// - Count and statistics selectors (enabled count, coverage percentage)
/// - Advanced feature selectors (quiet hours, lock screen, persistent)
/// - Validation and status selectors
/// - Error handling and edge cases
/// - State isolation between tests
///
/// **Testing Patterns:**
/// - ProviderContainer for isolated testing
/// - overrideWithBuild for AsyncNotifier mocking
/// - Comprehensive edge case coverage
/// - Performance validation through selector specificity
/// - Proper cleanup and disposal

/// Helper function to create mock build function for testing
/// Following Context7 MCP best practices for AsyncNotifier testing
FutureOr<NotificationSettingsState> Function(Ref, NotificationSettings) createMockBuild(
  NotificationSettingsState state,
) {
  return (ref, notifier) async => state;
}

/// Helper function to create mock build function that throws error
FutureOr<NotificationSettingsState> Function(Ref, NotificationSettings) createMockBuildError(Object error) {
  return (ref, notifier) async => throw error;
}

/// Helper function to create test state
NotificationSettingsState createTestState({
  bool notificationsEnabled = true,
  NotificationTiming globalTiming = NotificationTiming.exactTime,
  NotificationSound globalSound = NotificationSound.adhan,
  bool useVibration = true,
  Map<PrayerType, PrayerNotificationPreferences> prayerNotifications = const {},
}) {
  return NotificationSettingsState(
    notificationsEnabled: notificationsEnabled,
    globalTiming: globalTiming,
    globalSound: globalSound,
    useVibration: useVibration,
    prayerNotifications: prayerNotifications,
  );
}

void main() {
  group('Notification Settings Selectors', () {
    group('Core Notification Selectors', () {
      test('notificationsEnabled selector - data state', () async {
        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(createTestState(notificationsEnabled: true)),
            ),
          ],
        );
        addTearDown(container.dispose);

        // Wait for the provider to load
        await container.read(unifiedNotificationSettingsProvider.future);

        expect(container.read(notificationsEnabledProvider), true);
      });

      test('notificationsEnabled selector - disabled state', () async {
        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(createTestState(notificationsEnabled: false)),
            ),
          ],
        );
        addTearDown(container.dispose);

        // Wait for the provider to load
        await container.read(unifiedNotificationSettingsProvider.future);

        expect(container.read(notificationsEnabledProvider), false);
      });

      test('notificationsEnabled selector - loading state defaults to true', () {
        // For loading state, we test the selector's default behavior
        // when the provider hasn't loaded yet
        final container = ProviderContainer();
        addTearDown(container.dispose);

        // The selector should handle loading state gracefully with default value
        expect(container.read(notificationsEnabledProvider), true);
      });

      test('notificationsEnabled selector - error state defaults to true', () {
        final container = ProviderContainer(
          overrides: [unifiedNotificationSettingsProvider.overrideWithBuild(createMockBuildError('Test error'))],
        );
        addTearDown(container.dispose);

        expect(container.read(notificationsEnabledProvider), true);
      });

      test('globalNotificationTiming selector', () async {
        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(createTestState(globalTiming: NotificationTiming.fiveMinutesBefore)),
            ),
          ],
        );
        addTearDown(container.dispose);

        // Wait for the provider to load
        await container.read(unifiedNotificationSettingsProvider.future);

        expect(container.read(globalNotificationTimingProvider), NotificationTiming.fiveMinutesBefore);
      });

      test('globalNotificationTiming selector - loading defaults to exactTime', () {
        // For loading state, we test the selector's default behavior
        final container = ProviderContainer();
        addTearDown(container.dispose);

        expect(container.read(globalNotificationTimingProvider), NotificationTiming.exactTime);
      });

      test('globalNotificationSound selector', () async {
        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(createTestState(globalSound: NotificationSound.bell)),
            ),
          ],
        );
        addTearDown(container.dispose);

        // Wait for the provider to load
        await container.read(unifiedNotificationSettingsProvider.future);

        expect(container.read(globalNotificationSoundProvider), NotificationSound.bell);
      });

      test('globalNotificationSound selector - error defaults to adhan', () {
        final container = ProviderContainer(
          overrides: [unifiedNotificationSettingsProvider.overrideWithBuild(createMockBuildError('Test error'))],
        );
        addTearDown(container.dispose);

        expect(container.read(globalNotificationSoundProvider), NotificationSound.adhan);
      });

      test('globalVibrationEnabled selector', () async {
        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(createTestState(useVibration: false)),
            ),
          ],
        );
        addTearDown(container.dispose);

        // Wait for the provider to load
        await container.read(unifiedNotificationSettingsProvider.future);

        expect(container.read(globalVibrationEnabledProvider), false);
      });
    });

    group('Prayer-Specific Selectors', () {
      test('prayerNotifications selector', () async {
        final prayerNotifs = {
          PrayerType.fajr: const PrayerNotificationPreferences(enabled: true, timing: NotificationTiming.exactTime),
          PrayerType.dhuhr: const PrayerNotificationPreferences(
            enabled: false,
            timing: NotificationTiming.fiveMinutesBefore,
          ),
        };

        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(createTestState(prayerNotifications: prayerNotifs)),
            ),
          ],
        );
        addTearDown(container.dispose);

        // Wait for the provider to load
        await container.read(unifiedNotificationSettingsProvider.future);

        final result = container.read(prayerNotificationsProvider);
        expect(result, equals(prayerNotifs));
        expect(result[PrayerType.fajr]?.enabled, true);
        expect(result[PrayerType.dhuhr]?.enabled, false);
      });

      test('enabledPrayers selector', () async {
        final prayerNotifs = {
          PrayerType.fajr: const PrayerNotificationPreferences(enabled: true),
          PrayerType.dhuhr: const PrayerNotificationPreferences(enabled: false),
          PrayerType.asr: const PrayerNotificationPreferences(enabled: true),
          PrayerType.maghrib: const PrayerNotificationPreferences(enabled: false),
          PrayerType.isha: const PrayerNotificationPreferences(enabled: true),
        };

        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(createTestState(prayerNotifications: prayerNotifs)),
            ),
          ],
        );
        addTearDown(container.dispose);

        // Wait for the provider to load
        await container.read(unifiedNotificationSettingsProvider.future);

        final enabledPrayers = container.read(enabledPrayersProvider);
        expect(enabledPrayers.length, 3);
        expect(enabledPrayers, contains(PrayerType.fajr));
        expect(enabledPrayers, contains(PrayerType.asr));
        expect(enabledPrayers, contains(PrayerType.isha));
        expect(enabledPrayers, isNot(contains(PrayerType.dhuhr)));
        expect(enabledPrayers, isNot(contains(PrayerType.maghrib)));
      });

      test('enabledPrayers selector - global notifications disabled', () {
        final prayerNotifs = {
          PrayerType.fajr: const PrayerNotificationPreferences(enabled: true),
          PrayerType.dhuhr: const PrayerNotificationPreferences(enabled: true),
        };

        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(
                createTestState(
                  notificationsEnabled: false, // Global disabled
                  prayerNotifications: prayerNotifs,
                ),
              ),
            ),
          ],
        );
        addTearDown(container.dispose);

        expect(container.read(enabledPrayersProvider), isEmpty);
      });
    });

    group('Count and Statistics Selectors', () {
      test('enabledNotificationsCount selector', () async {
        final prayerNotifs = {
          PrayerType.fajr: const PrayerNotificationPreferences(enabled: true),
          PrayerType.dhuhr: const PrayerNotificationPreferences(enabled: false),
          PrayerType.asr: const PrayerNotificationPreferences(enabled: true),
          PrayerType.maghrib: const PrayerNotificationPreferences(enabled: true),
          PrayerType.isha: const PrayerNotificationPreferences(enabled: false),
        };

        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(createTestState(prayerNotifications: prayerNotifs)),
            ),
          ],
        );
        addTearDown(container.dispose);

        // Wait for the provider to load
        await container.read(unifiedNotificationSettingsProvider.future);

        expect(container.read(enabledNotificationsCountProvider), 3);
      });

      test('enabledNotificationsCount selector - global disabled', () {
        final prayerNotifs = {
          PrayerType.fajr: const PrayerNotificationPreferences(enabled: true),
          PrayerType.dhuhr: const PrayerNotificationPreferences(enabled: true),
        };

        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(createTestState(notificationsEnabled: false, prayerNotifications: prayerNotifs)),
            ),
          ],
        );
        addTearDown(container.dispose);

        expect(container.read(enabledNotificationsCountProvider), 0);
      });

      test('totalPrayersCount selector', () {
        final container = ProviderContainer();
        addTearDown(container.dispose);

        expect(container.read(totalPrayersCountProvider), PrayerType.values.length);
      });

      test('notificationCoveragePercentage selector', () async {
        final prayerNotifs = {
          PrayerType.fajr: const PrayerNotificationPreferences(enabled: true),
          PrayerType.dhuhr: const PrayerNotificationPreferences(enabled: true),
          PrayerType.asr: const PrayerNotificationPreferences(enabled: false),
          PrayerType.maghrib: const PrayerNotificationPreferences(enabled: false),
          PrayerType.isha: const PrayerNotificationPreferences(enabled: false),
        };

        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(createTestState(prayerNotifications: prayerNotifs)),
            ),
          ],
        );
        addTearDown(container.dispose);

        // Wait for the provider to load
        await container.read(unifiedNotificationSettingsProvider.future);

        // 2 out of 6 prayers enabled = 33.33%
        expect(container.read(notificationCoveragePercentageProvider), closeTo(33.33, 0.01));
      });

      test('notificationCoveragePercentage selector - no prayers configured', () {
        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(createTestState(prayerNotifications: {})),
            ),
          ],
        );
        addTearDown(container.dispose);

        expect(container.read(notificationCoveragePercentageProvider), 0.0);
      });
    });

    group('Individual Prayer Selectors', () {
      test('prayerNotificationEnabled selector', () async {
        final prayerNotifs = {
          PrayerType.fajr: const PrayerNotificationPreferences(enabled: true),
          PrayerType.dhuhr: const PrayerNotificationPreferences(enabled: false),
        };

        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(createTestState(prayerNotifications: prayerNotifs)),
            ),
          ],
        );
        addTearDown(container.dispose);

        // Wait for the provider to load
        await container.read(unifiedNotificationSettingsProvider.future);

        expect(container.read(prayerNotificationEnabledProvider(PrayerType.fajr)), true);
        expect(container.read(prayerNotificationEnabledProvider(PrayerType.dhuhr)), false);
      });

      test('prayerNotificationEnabled selector - global disabled', () {
        final prayerNotifs = {PrayerType.fajr: const PrayerNotificationPreferences(enabled: true)};

        final container = ProviderContainer(
          overrides: [
            unifiedNotificationSettingsProvider.overrideWithBuild(
              createMockBuild(createTestState(notificationsEnabled: false, prayerNotifications: prayerNotifs)),
            ),
          ],
        );
        addTearDown(container.dispose);

        expect(container.read(prayerNotificationEnabledProvider(PrayerType.fajr)), false);
      });
    });

    group('Validation and Status Selectors', () {
      test('notificationSettingsValid selector - data state', () {
        final container = ProviderContainer(
          overrides: [unifiedNotificationSettingsProvider.overrideWithBuild(createMockBuild(createTestState()))],
        );
        addTearDown(container.dispose);

        expect(container.read(notificationSettingsValidProvider), true);
      });

      test('notificationSettingsValid selector - loading defaults to true', () {
        final container = ProviderContainer();
        addTearDown(container.dispose);

        expect(container.read(notificationSettingsValidProvider), true);
      });

      test('notificationSettingsValid selector - error is false', () async {
        final container = ProviderContainer(
          overrides: [unifiedNotificationSettingsProvider.overrideWithBuild(createMockBuildError('Test error'))],
        );
        addTearDown(container.dispose);

        // Wait for the provider to attempt loading (and fail)
        try {
          await container.read(unifiedNotificationSettingsProvider.future);
        } catch (_) {
          // Expected to fail
        }

        expect(container.read(notificationSettingsValidProvider), false);
      });

      test('notificationSettingsLoading selector', () async {
        final loadingContainer = ProviderContainer();
        addTearDown(loadingContainer.dispose);

        expect(loadingContainer.read(notificationSettingsLoadingProvider), true);

        final dataContainer = ProviderContainer(
          overrides: [unifiedNotificationSettingsProvider.overrideWithBuild(createMockBuild(createTestState()))],
        );
        addTearDown(dataContainer.dispose);

        // Wait for the provider to load
        await dataContainer.read(unifiedNotificationSettingsProvider.future);

        expect(dataContainer.read(notificationSettingsLoadingProvider), false);
      });

      test('notificationSettingsError selector', () async {
        const testError = 'Test error message';

        final errorContainer = ProviderContainer(
          overrides: [unifiedNotificationSettingsProvider.overrideWithBuild(createMockBuildError(testError))],
        );
        addTearDown(errorContainer.dispose);

        // Wait for the provider to attempt loading (and fail)
        try {
          await errorContainer.read(unifiedNotificationSettingsProvider.future);
        } catch (_) {
          // Expected to fail
        }

        expect(errorContainer.read(notificationSettingsErrorProvider), testError);

        final dataContainer = ProviderContainer(
          overrides: [unifiedNotificationSettingsProvider.overrideWithBuild(createMockBuild(createTestState()))],
        );
        addTearDown(dataContainer.dispose);

        // Wait for the provider to load
        await dataContainer.read(unifiedNotificationSettingsProvider.future);

        expect(dataContainer.read(notificationSettingsErrorProvider), isNull);
      });
    });
  });
}
