import 'package:adhan/adhan.dart' hide Prayer;
import 'package:flutter/foundation.dart';

import '../../../features/prayer_times/domain/providers/custom_calculation_method_provider.dart';

/// Prayer Notification Settings
///
/// Comprehensive prayer notification settings model following Context7 MCP best practices
/// for managing prayer-specific notification preferences and behaviors.
///
/// This model provides granular control over prayer notifications including
/// individual prayer settings, reminder timings, and advanced notification features.
@immutable
class PrayerNotificationSettings {
  /// Whether prayer notifications are globally enabled
  final bool globallyEnabled;

  /// Individual prayer notification settings
  final Map<String, PrayerSettings> prayerSettings;

  /// Default reminder minutes before prayer time
  final List<int> defaultReminderMinutes;

  /// Default follow-up minutes after prayer time
  final List<int> defaultFollowUpMinutes;

  /// Whether to enable post-prayer reminders
  final bool enablePostPrayerReminders;

  /// Whether to enable daily prayer summary
  final bool enableDailySummary;

  /// Daily summary time (hour in 24-hour format)
  final int dailySummaryHour;

  /// Whether to enable weekly prayer schedule
  final bool enableWeeklySchedule;

  /// Calculation method for prayer times
  final CalculationMethod calculationMethod;

  /// Custom calculation method
  final CustomCalculationMethod customCalculationMethod;

  /// Notification sound settings
  final PrayerNotificationSoundSettings soundSettings;

  /// Advanced notification features
  final PrayerNotificationAdvancedSettings advancedSettings;

  /// Creates prayer notification settings with the specified configuration
  const PrayerNotificationSettings({
    this.globallyEnabled = true,
    this.prayerSettings = const {},
    this.defaultReminderMinutes = const [10],
    this.defaultFollowUpMinutes = const [5],
    this.enablePostPrayerReminders = false,
    this.enableDailySummary = true,
    this.dailySummaryHour = 6,
    this.enableWeeklySchedule = false,
    this.calculationMethod = CalculationMethod.other,
    this.customCalculationMethod = CustomCalculationMethod.jafari,
    this.soundSettings = const PrayerNotificationSoundSettings(),
    this.advancedSettings = const PrayerNotificationAdvancedSettings(),
  });

  /// Create default prayer notification settings
  factory PrayerNotificationSettings.defaultSettings() {
    return const PrayerNotificationSettings(
      globallyEnabled: true,
      prayerSettings: {
        'Fajr': PrayerSettings(enabled: true, reminderMinutes: [10, 5], enableSound: true, enableVibration: true),
        'Dhuhr': PrayerSettings(enabled: true, reminderMinutes: [10], enableSound: true, enableVibration: true),
        'Asr': PrayerSettings(enabled: true, reminderMinutes: [10], enableSound: true, enableVibration: true),
        'Maghrib': PrayerSettings(enabled: true, reminderMinutes: [10, 5], enableSound: true, enableVibration: true),
        'Isha': PrayerSettings(enabled: true, reminderMinutes: [10], enableSound: true, enableVibration: true),
      },
      defaultReminderMinutes: [10],
      defaultFollowUpMinutes: [5],
      enablePostPrayerReminders: false,
      enableDailySummary: true,
      dailySummaryHour: 6,
      enableWeeklySchedule: false,
      calculationMethod: CalculationMethod.other,
      customCalculationMethod: CustomCalculationMethod.jafari,
      soundSettings: PrayerNotificationSoundSettings(),
      advancedSettings: PrayerNotificationAdvancedSettings(),
    );
  }

  /// Create a copy with updated properties
  PrayerNotificationSettings copyWith({
    bool? globallyEnabled,
    Map<String, PrayerSettings>? prayerSettings,
    List<int>? defaultReminderMinutes,
    List<int>? defaultFollowUpMinutes,
    bool? enablePostPrayerReminders,
    bool? enableDailySummary,
    int? dailySummaryHour,
    bool? enableWeeklySchedule,
    CalculationMethod? calculationMethod,
    CustomCalculationMethod? customCalculationMethod,
    PrayerNotificationSoundSettings? soundSettings,
    PrayerNotificationAdvancedSettings? advancedSettings,
  }) {
    return PrayerNotificationSettings(
      globallyEnabled: globallyEnabled ?? this.globallyEnabled,
      prayerSettings: prayerSettings ?? this.prayerSettings,
      defaultReminderMinutes: defaultReminderMinutes ?? this.defaultReminderMinutes,
      defaultFollowUpMinutes: defaultFollowUpMinutes ?? this.defaultFollowUpMinutes,
      enablePostPrayerReminders: enablePostPrayerReminders ?? this.enablePostPrayerReminders,
      enableDailySummary: enableDailySummary ?? this.enableDailySummary,
      dailySummaryHour: dailySummaryHour ?? this.dailySummaryHour,
      enableWeeklySchedule: enableWeeklySchedule ?? this.enableWeeklySchedule,
      calculationMethod: calculationMethod ?? this.calculationMethod,
      customCalculationMethod: customCalculationMethod ?? this.customCalculationMethod,
      soundSettings: soundSettings ?? this.soundSettings,
      advancedSettings: advancedSettings ?? this.advancedSettings,
    );
  }

  /// Check if a specific prayer is enabled
  bool isPrayerEnabled(String prayerName) {
    if (!globallyEnabled) return false;

    final prayerSetting = prayerSettings[prayerName];
    return prayerSetting?.enabled ?? true; // Default to enabled
  }

  /// Get reminder minutes for a specific prayer
  List<int> getReminderMinutes(String prayerName) {
    final prayerSetting = prayerSettings[prayerName];
    return prayerSetting?.reminderMinutes ?? defaultReminderMinutes;
  }

  /// Get follow-up minutes for a specific prayer
  List<int> getFollowUpMinutes(String prayerName) {
    final prayerSetting = prayerSettings[prayerName];
    return prayerSetting?.followUpMinutes ?? defaultFollowUpMinutes;
  }

  /// Check if sound is enabled for a specific prayer
  bool isSoundEnabled(String prayerName) {
    final prayerSetting = prayerSettings[prayerName];
    return prayerSetting?.enableSound ?? soundSettings.globalSoundEnabled;
  }

  /// Check if vibration is enabled for a specific prayer
  bool isVibrationEnabled(String prayerName) {
    final prayerSetting = prayerSettings[prayerName];
    return prayerSetting?.enableVibration ?? soundSettings.globalVibrationEnabled;
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'globallyEnabled': globallyEnabled,
      'prayerSettings': prayerSettings.map((key, value) => MapEntry(key, value.toJson())),
      'defaultReminderMinutes': defaultReminderMinutes,
      'defaultFollowUpMinutes': defaultFollowUpMinutes,
      'enablePostPrayerReminders': enablePostPrayerReminders,
      'enableDailySummary': enableDailySummary,
      'dailySummaryHour': dailySummaryHour,
      'enableWeeklySchedule': enableWeeklySchedule,
      'calculationMethod': calculationMethod.index,
      'customCalculationMethod': customCalculationMethod.index,
      'soundSettings': soundSettings.toJson(),
      'advancedSettings': advancedSettings.toJson(),
    };
  }

  /// Validate settings consistency and correctness
  ///
  /// Context7 MCP: Comprehensive validation following defensive programming principles
  bool isValid() {
    try {
      // Validate reminder minutes list
      for (final minute in defaultReminderMinutes) {
        if (minute < 0 || minute > 1440) {
          return false; // Invalid reminder minutes (0-24 hours)
        }
      }

      // Validate follow-up minutes list
      for (final minute in defaultFollowUpMinutes) {
        if (minute < 0 || minute > 1440) {
          return false; // Invalid follow-up minutes (0-24 hours)
        }
      }

      if (dailySummaryHour < 0 || dailySummaryHour > 23) {
        return false; // Invalid hour (0-23)
      }

      // Validate prayer settings
      for (final prayerSetting in prayerSettings.values) {
        if (!_isValidPrayerSetting(prayerSetting)) {
          return false;
        }
      }

      // Validate sound settings
      if (!_isValidSoundSettings(soundSettings)) {
        return false;
      }

      // Validate advanced settings
      if (!_isValidAdvancedSettings(advancedSettings)) {
        return false;
      }

      return true;
    } on Exception {
      return false;
    }
  }

  /// Validate individual prayer setting
  bool _isValidPrayerSetting(PrayerSettings setting) {
    try {
      // Validate reminder minutes
      for (final minute in setting.reminderMinutes) {
        if (minute < 0 || minute > 1440) {
          return false;
        }
      }

      // Validate follow-up minutes
      for (final minute in setting.followUpMinutes) {
        if (minute < 0 || minute > 1440) {
          return false;
        }
      }

      return true;
    } on Exception {
      return false;
    }
  }

  /// Validate sound settings
  bool _isValidSoundSettings(PrayerNotificationSoundSettings settings) {
    try {
      if (settings.soundVolume < 0.0 || settings.soundVolume > 1.0) {
        return false;
      }

      if (settings.vibrationIntensity < 0.0 || settings.vibrationIntensity > 1.0) {
        return false;
      }

      return true;
    } on Exception {
      return false;
    }
  }

  /// Validate advanced settings
  bool _isValidAdvancedSettings(PrayerNotificationAdvancedSettings settings) {
    try {
      if (settings.maxNotificationsPerDay < 0 || settings.maxNotificationsPerDay > 100) {
        return false;
      }

      return true;
    } on Exception {
      return false;
    }
  }

  /// Create from JSON representation
  factory PrayerNotificationSettings.fromJson(Map<String, dynamic> json) {
    final prayerSettingsMap = <String, PrayerSettings>{};
    final prayerSettingsJson = json['prayerSettings'] as Map<String, dynamic>? ?? {};

    for (final entry in prayerSettingsJson.entries) {
      prayerSettingsMap[entry.key] = PrayerSettings.fromJson(entry.value as Map<String, dynamic>);
    }

    return PrayerNotificationSettings(
      globallyEnabled: json['globallyEnabled'] as bool? ?? true,
      prayerSettings: prayerSettingsMap,
      defaultReminderMinutes: List<int>.from(json['defaultReminderMinutes'] as List? ?? [10]),
      defaultFollowUpMinutes: List<int>.from(json['defaultFollowUpMinutes'] as List? ?? [5]),
      enablePostPrayerReminders: json['enablePostPrayerReminders'] as bool? ?? false,
      enableDailySummary: json['enableDailySummary'] as bool? ?? true,
      dailySummaryHour: json['dailySummaryHour'] as int? ?? 6,
      enableWeeklySchedule: json['enableWeeklySchedule'] as bool? ?? false,
      calculationMethod: CalculationMethod.values[json['calculationMethod'] as int? ?? CalculationMethod.other.index],
      customCalculationMethod: CustomCalculationMethod
          .values[json['customCalculationMethod'] as int? ?? CustomCalculationMethod.jafari.index],
      soundSettings: json['soundSettings'] != null
          ? PrayerNotificationSoundSettings.fromJson(json['soundSettings'] as Map<String, dynamic>)
          : const PrayerNotificationSoundSettings(),
      advancedSettings: json['advancedSettings'] != null
          ? PrayerNotificationAdvancedSettings.fromJson(json['advancedSettings'] as Map<String, dynamic>)
          : const PrayerNotificationAdvancedSettings(),
    );
  }

  @override
  String toString() {
    return 'PrayerNotificationSettings(globallyEnabled: $globallyEnabled, prayers: ${prayerSettings.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PrayerNotificationSettings &&
        other.globallyEnabled == globallyEnabled &&
        mapEquals(other.prayerSettings, prayerSettings) &&
        listEquals(other.defaultReminderMinutes, defaultReminderMinutes) &&
        listEquals(other.defaultFollowUpMinutes, defaultFollowUpMinutes) &&
        other.enablePostPrayerReminders == enablePostPrayerReminders &&
        other.enableDailySummary == enableDailySummary &&
        other.dailySummaryHour == dailySummaryHour &&
        other.enableWeeklySchedule == enableWeeklySchedule &&
        other.calculationMethod == calculationMethod &&
        other.customCalculationMethod == customCalculationMethod &&
        other.soundSettings == soundSettings &&
        other.advancedSettings == advancedSettings;
  }

  @override
  int get hashCode {
    return Object.hash(
      globallyEnabled,
      prayerSettings,
      defaultReminderMinutes,
      defaultFollowUpMinutes,
      enablePostPrayerReminders,
      enableDailySummary,
      dailySummaryHour,
      enableWeeklySchedule,
      calculationMethod,
      customCalculationMethod,
      soundSettings,
      advancedSettings,
    );
  }
}

/// Individual Prayer Settings
///
/// Settings for individual prayers with customizable notification behavior.
@immutable
class PrayerSettings {
  /// Whether notifications are enabled for this prayer
  final bool enabled;

  /// Reminder minutes before prayer time
  final List<int> reminderMinutes;

  /// Follow-up minutes after prayer time
  final List<int> followUpMinutes;

  /// Whether to enable sound for this prayer
  final bool enableSound;

  /// Whether to enable vibration for this prayer
  final bool enableVibration;

  /// Custom sound file path for this prayer
  final String? customSoundPath;

  /// Custom vibration pattern for this prayer
  final List<int>? customVibrationPattern;

  /// Creates prayer settings with the specified configuration
  const PrayerSettings({
    this.enabled = true,
    this.reminderMinutes = const [10],
    this.followUpMinutes = const [5],
    this.enableSound = true,
    this.enableVibration = true,
    this.customSoundPath,
    this.customVibrationPattern,
  });

  /// Create a copy with updated properties
  PrayerSettings copyWith({
    bool? enabled,
    List<int>? reminderMinutes,
    List<int>? followUpMinutes,
    bool? enableSound,
    bool? enableVibration,
    String? customSoundPath,
    List<int>? customVibrationPattern,
  }) {
    return PrayerSettings(
      enabled: enabled ?? this.enabled,
      reminderMinutes: reminderMinutes ?? this.reminderMinutes,
      followUpMinutes: followUpMinutes ?? this.followUpMinutes,
      enableSound: enableSound ?? this.enableSound,
      enableVibration: enableVibration ?? this.enableVibration,
      customSoundPath: customSoundPath ?? this.customSoundPath,
      customVibrationPattern: customVibrationPattern ?? this.customVibrationPattern,
    );
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'enabled': enabled,
      'reminderMinutes': reminderMinutes,
      'followUpMinutes': followUpMinutes,
      'enableSound': enableSound,
      'enableVibration': enableVibration,
      'customSoundPath': customSoundPath,
      'customVibrationPattern': customVibrationPattern,
    };
  }

  /// Create from JSON representation
  factory PrayerSettings.fromJson(Map<String, dynamic> json) {
    return PrayerSettings(
      enabled: json['enabled'] as bool? ?? true,
      reminderMinutes: List<int>.from(json['reminderMinutes'] as List? ?? [10]),
      followUpMinutes: List<int>.from(json['followUpMinutes'] as List? ?? [5]),
      enableSound: json['enableSound'] as bool? ?? true,
      enableVibration: json['enableVibration'] as bool? ?? true,
      customSoundPath: json['customSoundPath'] as String?,
      customVibrationPattern: (json['customVibrationPattern'] as List<dynamic>?)?.map((e) => e as int).toList(),
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PrayerSettings &&
        other.enabled == enabled &&
        listEquals(other.reminderMinutes, reminderMinutes) &&
        listEquals(other.followUpMinutes, followUpMinutes) &&
        other.enableSound == enableSound &&
        other.enableVibration == enableVibration &&
        other.customSoundPath == customSoundPath &&
        listEquals(other.customVibrationPattern, customVibrationPattern);
  }

  @override
  int get hashCode {
    return Object.hash(
      enabled,
      reminderMinutes,
      followUpMinutes,
      enableSound,
      enableVibration,
      customSoundPath,
      customVibrationPattern,
    );
  }
}

/// Prayer Notification Sound Settings
///
/// Global sound and vibration settings for prayer notifications.
@immutable
class PrayerNotificationSoundSettings {
  /// Whether sound is globally enabled
  final bool globalSoundEnabled;

  /// Whether vibration is globally enabled
  final bool globalVibrationEnabled;

  /// Default sound volume (0.0 to 1.0)
  final double soundVolume;

  /// Default vibration intensity (0.0 to 1.0)
  final double vibrationIntensity;

  /// Whether to use system notification sound
  final bool useSystemSound;

  /// Whether to use system vibration pattern
  final bool useSystemVibration;

  /// Creates prayer notification sound settings with the specified configuration
  const PrayerNotificationSoundSettings({
    this.globalSoundEnabled = true,
    this.globalVibrationEnabled = true,
    this.soundVolume = 0.8,
    this.vibrationIntensity = 0.8,
    this.useSystemSound = true,
    this.useSystemVibration = true,
  });

  /// Create a copy with updated properties
  PrayerNotificationSoundSettings copyWith({
    bool? globalSoundEnabled,
    bool? globalVibrationEnabled,
    double? soundVolume,
    double? vibrationIntensity,
    bool? useSystemSound,
    bool? useSystemVibration,
  }) {
    return PrayerNotificationSoundSettings(
      globalSoundEnabled: globalSoundEnabled ?? this.globalSoundEnabled,
      globalVibrationEnabled: globalVibrationEnabled ?? this.globalVibrationEnabled,
      soundVolume: soundVolume ?? this.soundVolume,
      vibrationIntensity: vibrationIntensity ?? this.vibrationIntensity,
      useSystemSound: useSystemSound ?? this.useSystemSound,
      useSystemVibration: useSystemVibration ?? this.useSystemVibration,
    );
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'globalSoundEnabled': globalSoundEnabled,
      'globalVibrationEnabled': globalVibrationEnabled,
      'soundVolume': soundVolume,
      'vibrationIntensity': vibrationIntensity,
      'useSystemSound': useSystemSound,
      'useSystemVibration': useSystemVibration,
    };
  }

  /// Create from JSON representation
  factory PrayerNotificationSoundSettings.fromJson(Map<String, dynamic> json) {
    return PrayerNotificationSoundSettings(
      globalSoundEnabled: json['globalSoundEnabled'] as bool? ?? true,
      globalVibrationEnabled: json['globalVibrationEnabled'] as bool? ?? true,
      soundVolume: (json['soundVolume'] as num?)?.toDouble() ?? 0.8,
      vibrationIntensity: (json['vibrationIntensity'] as num?)?.toDouble() ?? 0.8,
      useSystemSound: json['useSystemSound'] as bool? ?? true,
      useSystemVibration: json['useSystemVibration'] as bool? ?? true,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PrayerNotificationSoundSettings &&
        other.globalSoundEnabled == globalSoundEnabled &&
        other.globalVibrationEnabled == globalVibrationEnabled &&
        other.soundVolume == soundVolume &&
        other.vibrationIntensity == vibrationIntensity &&
        other.useSystemSound == useSystemSound &&
        other.useSystemVibration == useSystemVibration;
  }

  @override
  int get hashCode {
    return Object.hash(
      globalSoundEnabled,
      globalVibrationEnabled,
      soundVolume,
      vibrationIntensity,
      useSystemSound,
      useSystemVibration,
    );
  }
}

/// Prayer Notification Advanced Settings
///
/// Advanced features and behaviors for prayer notifications.
@immutable
class PrayerNotificationAdvancedSettings {
  /// Whether to enable smart scheduling
  final bool enableSmartScheduling;

  /// Whether to enable location-based adjustments
  final bool enableLocationAdjustments;

  /// Whether to enable weather-based adjustments
  final bool enableWeatherAdjustments;

  /// Whether to enable adaptive timing
  final bool enableAdaptiveTiming;

  /// Whether to enable notification bundling
  final bool enableNotificationBundling;

  /// Maximum number of notifications per day
  final int maxNotificationsPerDay;

  /// Creates prayer notification advanced settings with the specified configuration
  const PrayerNotificationAdvancedSettings({
    this.enableSmartScheduling = false,
    this.enableLocationAdjustments = false,
    this.enableWeatherAdjustments = false,
    this.enableAdaptiveTiming = false,
    this.enableNotificationBundling = true,
    this.maxNotificationsPerDay = 50,
  });

  /// Create a copy with updated properties
  PrayerNotificationAdvancedSettings copyWith({
    bool? enableSmartScheduling,
    bool? enableLocationAdjustments,
    bool? enableWeatherAdjustments,
    bool? enableAdaptiveTiming,
    bool? enableNotificationBundling,
    int? maxNotificationsPerDay,
  }) {
    return PrayerNotificationAdvancedSettings(
      enableSmartScheduling: enableSmartScheduling ?? this.enableSmartScheduling,
      enableLocationAdjustments: enableLocationAdjustments ?? this.enableLocationAdjustments,
      enableWeatherAdjustments: enableWeatherAdjustments ?? this.enableWeatherAdjustments,
      enableAdaptiveTiming: enableAdaptiveTiming ?? this.enableAdaptiveTiming,
      enableNotificationBundling: enableNotificationBundling ?? this.enableNotificationBundling,
      maxNotificationsPerDay: maxNotificationsPerDay ?? this.maxNotificationsPerDay,
    );
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'enableSmartScheduling': enableSmartScheduling,
      'enableLocationAdjustments': enableLocationAdjustments,
      'enableWeatherAdjustments': enableWeatherAdjustments,
      'enableAdaptiveTiming': enableAdaptiveTiming,
      'enableNotificationBundling': enableNotificationBundling,
      'maxNotificationsPerDay': maxNotificationsPerDay,
    };
  }

  /// Create from JSON representation
  factory PrayerNotificationAdvancedSettings.fromJson(Map<String, dynamic> json) {
    return PrayerNotificationAdvancedSettings(
      enableSmartScheduling: json['enableSmartScheduling'] as bool? ?? false,
      enableLocationAdjustments: json['enableLocationAdjustments'] as bool? ?? false,
      enableWeatherAdjustments: json['enableWeatherAdjustments'] as bool? ?? false,
      enableAdaptiveTiming: json['enableAdaptiveTiming'] as bool? ?? false,
      enableNotificationBundling: json['enableNotificationBundling'] as bool? ?? true,
      maxNotificationsPerDay: json['maxNotificationsPerDay'] as int? ?? 50,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PrayerNotificationAdvancedSettings &&
        other.enableSmartScheduling == enableSmartScheduling &&
        other.enableLocationAdjustments == enableLocationAdjustments &&
        other.enableWeatherAdjustments == enableWeatherAdjustments &&
        other.enableAdaptiveTiming == enableAdaptiveTiming &&
        other.enableNotificationBundling == enableNotificationBundling &&
        other.maxNotificationsPerDay == maxNotificationsPerDay;
  }

  @override
  int get hashCode {
    return Object.hash(
      enableSmartScheduling,
      enableLocationAdjustments,
      enableWeatherAdjustments,
      enableAdaptiveTiming,
      enableNotificationBundling,
      maxNotificationsPerDay,
    );
  }
}
