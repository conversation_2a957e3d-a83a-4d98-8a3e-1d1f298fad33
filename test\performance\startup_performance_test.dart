import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:integration_test/integration_test.dart';

import 'package:masajid_albahrain/core/providers/theme/theme_settings_provider.dart';
// Context7 MCP: Updated to use unified notification settings provider
import 'package:masajid_albahrain/core/notifications/providers/unified_notification_provider.dart';
import 'package:masajid_albahrain/core/providers/prayer_times/prayer_times_settings_provider.dart';
import 'package:masajid_albahrain/core/providers/location/location_settings_provider.dart';
import 'package:masajid_albahrain/core/providers/performance/performance_settings_provider.dart';
import 'package:masajid_albahrain/core/providers/advanced/advanced_settings_provider.dart';

/// Startup performance testing suite
/// Following Context7 MCP best practices for app initialization validation
class StartupPerformanceTest {
  static const String testTag = 'startup-performance';

  /// Test configuration
  static const int measurementIterations = 10;
  static const Duration testTimeout = Duration(minutes: 5);

  /// Performance thresholds (in milliseconds)
  static const double maxColdStartTime = 3000.0; // 3 seconds
  static const double maxWarmStartTime = 1000.0; // 1 second
  static const double maxProviderInitTime = 500.0; // 500ms
  static const double maxFirstFrameTime = 2000.0; // 2 seconds

  /// Startup phases
  static const String phaseProviderInit = 'provider_initialization';
  static const String phaseFirstFrame = 'first_frame';
  static const String phaseFullyLoaded = 'fully_loaded';
}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Startup Performance Tests', () {
    testWidgets('Cold Start Performance', (WidgetTester tester) async {
      final startupMetrics = <String, double>{};
      final stopwatch = Stopwatch();

      // Measure cold start time
      stopwatch.start();

      // Phase 1: Provider initialization
      final providerInitStart = stopwatch.elapsedMilliseconds;

      final container = ProviderContainer();

      // Initialize all providers (simulating app startup)
      await Future.wait([
        Future(() => container.read(themeSettingsProvider)),
        Future(() => container.read(notificationSettingsProvider)),
        Future(() => container.read(prayerTimesSettingsProvider)),
        Future(() => container.read(locationSettingsProvider)),
        Future(() => container.read(performanceSettingsProvider)),
        Future(() => container.read(advancedSettingsProvider)),
      ]);

      final providerInitEnd = stopwatch.elapsedMilliseconds;
      startupMetrics[StartupPerformanceTest.phaseProviderInit] = (providerInitEnd - providerInitStart).toDouble();

      // Phase 2: First frame rendering
      final firstFrameStart = stopwatch.elapsedMilliseconds;

      await tester.pumpWidget(
        ProviderScope(
          overrides: [],
          child: MaterialApp(
            home: Consumer(
              builder: (context, ref, child) {
                // Watch all providers to simulate real app usage
                ref.watch(themeSettingsProvider);
                ref.watch(notificationSettingsProvider);
                ref.watch(prayerTimesSettingsProvider);
                ref.watch(locationSettingsProvider);
                ref.watch(performanceSettingsProvider);
                ref.watch(advancedSettingsProvider);

                return const Scaffold(body: Center(child: Text('App Loaded')));
              },
            ),
          ),
        ),
      );

      final firstFrameEnd = stopwatch.elapsedMilliseconds;
      startupMetrics[StartupPerformanceTest.phaseFirstFrame] = (firstFrameEnd - firstFrameStart).toDouble();

      // Phase 3: Fully loaded (after all animations and async operations)
      await tester.pumpAndSettle();

      stopwatch.stop();
      final totalStartupTime = stopwatch.elapsedMilliseconds.toDouble();
      startupMetrics[StartupPerformanceTest.phaseFullyLoaded] = totalStartupTime;

      // Log performance metrics
      debugPrint('📊 Cold Start Performance Metrics:');
      debugPrint('   Provider Init: ${startupMetrics[StartupPerformanceTest.phaseProviderInit]!.toStringAsFixed(2)}ms');
      debugPrint('   First Frame: ${startupMetrics[StartupPerformanceTest.phaseFirstFrame]!.toStringAsFixed(2)}ms');
      debugPrint('   Total Time: ${startupMetrics[StartupPerformanceTest.phaseFullyLoaded]!.toStringAsFixed(2)}ms');

      // Validate performance thresholds
      expect(
        startupMetrics[StartupPerformanceTest.phaseProviderInit]!,
        lessThan(StartupPerformanceTest.maxProviderInitTime),
        reason: 'Provider initialization time exceeds threshold',
      );

      expect(
        startupMetrics[StartupPerformanceTest.phaseFirstFrame]!,
        lessThan(StartupPerformanceTest.maxFirstFrameTime),
        reason: 'First frame rendering time exceeds threshold',
      );

      expect(
        startupMetrics[StartupPerformanceTest.phaseFullyLoaded]!,
        lessThan(StartupPerformanceTest.maxColdStartTime),
        reason: 'Total cold start time exceeds threshold',
      );

      container.dispose();
    }, timeout: StartupPerformanceTest.testTimeout);

    testWidgets('Warm Start Performance', (WidgetTester tester) async {
      final warmStartTimes = <double>[];

      // Pre-initialize providers (simulating warm start scenario)
      final container = ProviderContainer();
      container.read(themeSettingsProvider);
      container.read(notificationSettingsProvider);
      container.read(prayerTimesSettingsProvider);
      container.read(locationSettingsProvider);
      container.read(performanceSettingsProvider);
      container.read(advancedSettingsProvider);

      // Measure warm start times
      for (int i = 0; i < StartupPerformanceTest.measurementIterations; i++) {
        final stopwatch = Stopwatch();
        stopwatch.start();

        await tester.pumpWidget(
          ProviderScope(
            parent: container,
            child: MaterialApp(
              home: Consumer(
                builder: (context, ref, child) {
                  // Watch providers (they should already be initialized)
                  ref.watch(themeSettingsProvider);
                  ref.watch(notificationSettingsProvider);

                  return const Scaffold(body: Center(child: Text('Warm Start')));
                },
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();
        stopwatch.stop();

        warmStartTimes.add(stopwatch.elapsedMilliseconds.toDouble());

        // Clear the widget tree for next iteration
        await tester.pumpWidget(Container());
      }

      final averageWarmStart = warmStartTimes.reduce((a, b) => a + b) / warmStartTimes.length;
      final maxWarmStart = warmStartTimes.reduce((a, b) => a > b ? a : b);
      final minWarmStart = warmStartTimes.reduce((a, b) => a < b ? a : b);

      debugPrint('📊 Warm Start Performance:');
      debugPrint('   Average: ${averageWarmStart.toStringAsFixed(2)}ms');
      debugPrint('   Maximum: ${maxWarmStart.toStringAsFixed(2)}ms');
      debugPrint('   Minimum: ${minWarmStart.toStringAsFixed(2)}ms');
      debugPrint('   Threshold: ${StartupPerformanceTest.maxWarmStartTime}ms');

      expect(
        averageWarmStart,
        lessThan(StartupPerformanceTest.maxWarmStartTime),
        reason: 'Average warm start time exceeds threshold',
      );
      expect(
        maxWarmStart,
        lessThan(StartupPerformanceTest.maxWarmStartTime * 2),
        reason: 'Maximum warm start time exceeds threshold',
      );

      container.dispose();
    }, timeout: StartupPerformanceTest.testTimeout);

    testWidgets('Provider Dependency Resolution Performance', (WidgetTester tester) async {
      final resolutionTimes = <double>[];

      for (int i = 0; i < StartupPerformanceTest.measurementIterations; i++) {
        final container = ProviderContainer();
        final stopwatch = Stopwatch();

        stopwatch.start();

        // Simulate complex dependency resolution
        final futures = <Future>[];

        // Create multiple dependent reads
        for (int j = 0; j < 10; j++) {
          futures.add(
            Future(() {
              container.read(themeSettingsProvider);
              container.read(notificationSettingsProvider);
              container.read(prayerTimesSettingsProvider);
            }),
          );
        }

        await Future.wait(futures);

        stopwatch.stop();
        resolutionTimes.add(stopwatch.elapsedMilliseconds.toDouble());

        container.dispose();
      }

      final averageResolutionTime = resolutionTimes.reduce((a, b) => a + b) / resolutionTimes.length;
      final maxResolutionTime = resolutionTimes.reduce((a, b) => a > b ? a : b);

      debugPrint('📊 Provider Dependency Resolution Performance:');
      debugPrint('   Average: ${averageResolutionTime.toStringAsFixed(2)}ms');
      debugPrint('   Maximum: ${maxResolutionTime.toStringAsFixed(2)}ms');

      expect(averageResolutionTime, lessThan(100.0), reason: 'Provider dependency resolution time is too high');
    }, timeout: StartupPerformanceTest.testTimeout);

    testWidgets('Memory Allocation During Startup', (WidgetTester tester) async {
      final initialMemory = _getCurrentMemoryUsage();

      final container = ProviderContainer();

      // Initialize all providers and measure memory allocation
      container.read(themeSettingsProvider);
      container.read(notificationSettingsProvider);
      container.read(prayerTimesSettingsProvider);
      container.read(locationSettingsProvider);
      container.read(performanceSettingsProvider);
      container.read(advancedSettingsProvider);

      await tester.pumpWidget(
        ProviderScope(
          parent: container,
          child: MaterialApp(
            home: Consumer(
              builder: (context, ref, child) {
                ref.watch(themeSettingsProvider);
                ref.watch(notificationSettingsProvider);
                ref.watch(prayerTimesSettingsProvider);
                ref.watch(locationSettingsProvider);
                ref.watch(performanceSettingsProvider);
                ref.watch(advancedSettingsProvider);

                return const Scaffold(body: Center(child: Text('Memory Test')));
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      final finalMemory = _getCurrentMemoryUsage();
      final memoryIncrease = finalMemory - initialMemory;

      debugPrint('📊 Startup Memory Allocation:');
      debugPrint('   Initial: ${(initialMemory / 1024 / 1024).toStringAsFixed(2)}MB');
      debugPrint('   Final: ${(finalMemory / 1024 / 1024).toStringAsFixed(2)}MB');
      debugPrint('   Increase: ${(memoryIncrease / 1024 / 1024).toStringAsFixed(2)}MB');

      // Validate memory increase is reasonable for startup
      expect(
        memoryIncrease,
        lessThan(20 * 1024 * 1024), // Less than 20MB
        reason: 'Startup memory allocation is too high',
      );

      container.dispose();
    }, timeout: StartupPerformanceTest.testTimeout);
  }, tags: [StartupPerformanceTest.testTag]);
}

/// Helper function to get current memory usage
int _getCurrentMemoryUsage() {
  if (kIsWeb) {
    return 0;
  }

  try {
    return ProcessInfo.currentRss;
  } catch (e) {
    return 0;
  }
}
