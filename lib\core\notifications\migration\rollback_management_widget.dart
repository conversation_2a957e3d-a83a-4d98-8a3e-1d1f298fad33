import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'rollback_procedures.dart';

/// Rollback Management Widget
///
/// **Task 4.1.5: Create rollback procedures for emergency situations**
///
/// This widget provides comprehensive rollback management interface for developers
/// following Context7 MCP patterns for emergency situation handling and system recovery.
///
/// Features:
/// - Emergency rollback controls with instant system restoration
/// - System snapshot management with point-in-time recovery
/// - Health monitoring dashboard with real-time status updates
/// - Rollback operation tracking with detailed progress monitoring
/// - Manual rollback triggers with confirmation workflows
/// - Automated rollback configuration with threshold management
/// - System integrity validation with comprehensive checks
/// - Rollback history and analytics with detailed audit trails
/// - Context7 MCP compliance with dependency injection
/// - Developer-friendly interface with intuitive controls
class RollbackManagementWidget extends ConsumerStatefulWidget {
  const RollbackManagementWidget({super.key});

  @override
  ConsumerState<RollbackManagementWidget> createState() => _RollbackManagementWidgetState();
}

class _RollbackManagementWidgetState extends ConsumerState<RollbackManagementWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeRollbackSystem();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Initialize rollback system
  Future<void> _initializeRollbackSystem() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final service = ref.read(rollbackProceduresProvider);
      await service.initialize();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Rollback Management'),
        backgroundColor: Colors.red.shade700,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _initializeRollbackSystem,
            tooltip: 'Refresh Status',
          ),
          IconButton(
            icon: const Icon(Icons.emergency),
            onPressed: _showEmergencyRollbackDialog,
            tooltip: 'Emergency Rollback',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.health_and_safety), text: 'Health'),
            Tab(icon: Icon(Icons.camera_alt), text: 'Snapshots'),
            Tab(icon: Icon(Icons.history), text: 'Operations'),
            Tab(icon: Icon(Icons.settings), text: 'Settings'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorWidget()
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildHealthTab(),
                    _buildSnapshotsTab(),
                    _buildOperationsTab(),
                    _buildSettingsTab(),
                  ],
                ),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error, size: 64, color: Colors.red.shade400),
          const SizedBox(height: 16),
          Text(
            'Failed to load rollback system',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey.shade600,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _initializeRollbackSystem,
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  /// Build health tab
  Widget _buildHealthTab() {
    return Consumer(
      builder: (context, ref, child) {
        final healthAsync = ref.watch(systemHealthProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHealthOverviewCard(),
              const SizedBox(height: 16),
              healthAsync.when(
                data: (healthResults) => _buildHealthResultsSection(healthResults),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, _) => _buildHealthErrorWidget(error),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Build health overview card
  Widget _buildHealthOverviewCard() {
    final service = ref.read(rollbackProceduresProvider);
    final isEmergencyMode = service.isEmergencyMode;

    return Card(
      color: isEmergencyMode ? Colors.red.shade50 : null,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isEmergencyMode ? Icons.emergency : Icons.health_and_safety,
                  color: isEmergencyMode ? Colors.red.shade600 : Colors.green.shade600,
                ),
                const SizedBox(width: 8),
                Text(
                  isEmergencyMode ? 'EMERGENCY MODE ACTIVE' : 'System Health Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isEmergencyMode ? Colors.red.shade800 : null,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (isEmergencyMode) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red.shade100,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red.shade300),
                ),
                child: Row(
                  children: [
                    Icon(Icons.warning, color: Colors.red.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'System is in emergency mode. Some features may be disabled.',
                        style: TextStyle(
                          color: Colors.red.shade800,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ],
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _createSystemSnapshot,
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('Create Snapshot'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue.shade600,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _validateSystemIntegrity,
                    icon: const Icon(Icons.verified),
                    label: const Text('Validate System'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green.shade600,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build health results section
  Widget _buildHealthResultsSection(Map<String, HealthCheckResult> healthResults) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Component Health Status',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ...healthResults.entries.map((entry) => _buildHealthResultCard(entry.value)),
      ],
    );
  }

  /// Build health result card
  Widget _buildHealthResultCard(HealthCheckResult result) {
    final isHealthy = result.isHealthy;
    final color = isHealthy ? Colors.green : Colors.red;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isHealthy ? Icons.check_circle : Icons.error,
                  color: color.shade600,
                ),
                const SizedBox(width: 8),
                Text(
                  result.component.toUpperCase(),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Chip(
                  label: Text(result.status.toUpperCase()),
                  backgroundColor: color.shade100,
                  labelStyle: TextStyle(
                    color: color.shade800,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
            if (result.error != null) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Error: ${result.error}',
                  style: TextStyle(color: Colors.red.shade700, fontSize: 12),
                ),
              ),
            ],
            if (result.metrics.isNotEmpty) ...[
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                children: result.metrics.entries.map((metric) {
                  return Chip(
                    label: Text('${metric.key}: ${metric.value}'),
                    backgroundColor: Colors.grey.shade100,
                    labelStyle: const TextStyle(fontSize: 11),
                  );
                }).toList(),
              ),
            ],
            const SizedBox(height: 8),
            Text(
              'Last checked: ${_formatTime(result.timestamp)}',
              style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  /// Build health error widget
  Widget _buildHealthErrorWidget(Object error) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(Icons.error, size: 48, color: Colors.red.shade400),
            const SizedBox(height: 16),
            Text(
              'Failed to load health status',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: TextStyle(color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build snapshots tab
  Widget _buildSnapshotsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final snapshotsAsync = ref.watch(systemSnapshotsProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Text(
                    'System Snapshots',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  ElevatedButton.icon(
                    onPressed: _createSystemSnapshot,
                    icon: const Icon(Icons.add),
                    label: const Text('Create Snapshot'),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              snapshotsAsync.when(
                data: (snapshots) => _buildSnapshotsList(snapshots),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, _) => _buildSnapshotsErrorWidget(error),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Build snapshots list
  Widget _buildSnapshotsList(List<SystemSnapshot> snapshots) {
    if (snapshots.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Center(
            child: Column(
              children: [
                Icon(Icons.camera_alt, size: 48, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'No snapshots available',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
                SizedBox(height: 8),
                Text(
                  'Create a snapshot to enable rollback functionality',
                  style: TextStyle(color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Column(
      children: snapshots.map((snapshot) => _buildSnapshotCard(snapshot)).toList(),
    );
  }

  /// Build snapshot card
  Widget _buildSnapshotCard(SystemSnapshot snapshot) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.camera_alt, color: Colors.blue.shade600),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    snapshot.description,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleSnapshotAction(value, snapshot),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'rollback',
                      child: Row(
                        children: [
                          Icon(Icons.restore),
                          SizedBox(width: 8),
                          Text('Rollback to this snapshot'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete snapshot'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Created: ${_formatDateTime(snapshot.timestamp)}',
              style: TextStyle(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 8),
            Text(
              'ID: ${snapshot.id}',
              style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
            ),
            const SizedBox(height: 8),
            Text(
              'Checksum: ${snapshot.checksum}',
              style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: [
                Chip(
                  label: Text('Flags: ${snapshot.featureFlagStates.length}'),
                  backgroundColor: Colors.blue.shade100,
                  labelStyle: TextStyle(color: Colors.blue.shade800, fontSize: 11),
                ),
                Chip(
                  label: Text('Providers: ${snapshot.providerConfigurations.length}'),
                  backgroundColor: Colors.green.shade100,
                  labelStyle: TextStyle(color: Colors.green.shade800, fontSize: 11),
                ),
                Chip(
                  label: Text('Settings: ${snapshot.systemSettings.length}'),
                  backgroundColor: Colors.orange.shade100,
                  labelStyle: TextStyle(color: Colors.orange.shade800, fontSize: 11),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build snapshots error widget
  Widget _buildSnapshotsErrorWidget(Object error) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(Icons.error, size: 48, color: Colors.red.shade400),
            const SizedBox(height: 16),
            Text(
              'Failed to load snapshots',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: TextStyle(color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build operations tab
  Widget _buildOperationsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final operationsAsync = ref.watch(rollbackOperationsProvider);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Rollback Operations',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              operationsAsync.when(
                data: (operations) => _buildOperationsList(operations),
                loading: () => const Center(child: CircularProgressIndicator()),
                error: (error, _) => _buildOperationsErrorWidget(error),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Build operations list
  Widget _buildOperationsList(List<RollbackOperation> operations) {
    if (operations.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Center(
            child: Column(
              children: [
                Icon(Icons.history, size: 48, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'No rollback operations',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
                SizedBox(height: 8),
                Text(
                  'Rollback operations will appear here when executed',
                  style: TextStyle(color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Column(
      children: operations.map((operation) => _buildOperationCard(operation)).toList(),
    );
  }

  /// Build operation card
  Widget _buildOperationCard(RollbackOperation operation) {
    final statusColor = _getStatusColor(operation.status);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(_getStatusIcon(operation.status), color: statusColor),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    operation.event.description,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
                Chip(
                  label: Text(operation.status.name.toUpperCase()),
                  backgroundColor: statusColor.withOpacity(0.1),
                  labelStyle: TextStyle(
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 11,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Started: ${_formatDateTime(operation.startTime)}',
              style: TextStyle(color: Colors.grey.shade600),
            ),
            if (operation.endTime != null) ...[
              Text(
                'Completed: ${_formatDateTime(operation.endTime!)}',
                style: TextStyle(color: Colors.grey.shade600),
              ),
              Text(
                'Duration: ${operation.endTime!.difference(operation.startTime).inSeconds}s',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ],
            const SizedBox(height: 8),
            Text(
              'Trigger: ${operation.event.triggerType.name} | Severity: ${operation.event.severity.name} | Scope: ${operation.event.scope.name}',
              style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
            ),
            if (operation.steps.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Progress: ${operation.completedSteps.length}/${operation.steps.length} steps',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              const SizedBox(height: 8),
              LinearProgressIndicator(
                value: operation.steps.isEmpty ? 0 : operation.completedSteps.length / operation.steps.length,
                backgroundColor: Colors.grey.shade300,
                valueColor: AlwaysStoppedAnimation<Color>(statusColor),
              ),
            ],
            if (operation.error != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  'Error: ${operation.error}',
                  style: TextStyle(color: Colors.red.shade700, fontSize: 12),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build operations error widget
  Widget _buildOperationsErrorWidget(Object error) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(Icons.error, size: 48, color: Colors.red.shade400),
            const SizedBox(height: 16),
            Text(
              'Failed to load operations',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: TextStyle(color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build settings tab
  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Rollback Settings',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Emergency Controls',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: _showEmergencyRollbackDialog,
                    icon: const Icon(Icons.emergency),
                    label: const Text('Emergency Rollback'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red.shade600,
                      foregroundColor: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Immediately rollback the entire system to the last known good state.',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Automated Rollback',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 16),
                  SwitchListTile(
                    title: const Text('Enable Automatic Rollback'),
                    subtitle: const Text('Automatically rollback on critical failures'),
                    value: true, // This would be configurable
                    onChanged: (value) {
                      // Handle automatic rollback toggle
                    },
                  ),
                  SwitchListTile(
                    title: const Text('Health Monitoring'),
                    subtitle: const Text('Continuously monitor system health'),
                    value: true, // This would be configurable
                    onChanged: (value) {
                      // Handle health monitoring toggle
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Create system snapshot
  Future<void> _createSystemSnapshot() async {
    try {
      final service = ref.read(rollbackProceduresProvider);
      await service.createSystemSnapshot('Manual snapshot - ${DateTime.now()}');
      
      // Refresh snapshots
      ref.invalidate(systemSnapshotsProvider);
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('System snapshot created successfully'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to create snapshot: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Validate system integrity
  Future<void> _validateSystemIntegrity() async {
    try {
      final service = ref.read(rollbackProceduresProvider);
      final isValid = await service.validateSystemIntegrity();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(isValid ? 'System integrity validated' : 'System integrity issues detected'),
          backgroundColor: isValid ? Colors.green : Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to validate system: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Show emergency rollback dialog
  void _showEmergencyRollbackDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.emergency, color: Colors.red),
            SizedBox(width: 8),
            Text('Emergency Rollback'),
          ],
        ),
        content: const Text(
          'This will immediately rollback the entire system to the last known good state. '
          'This action cannot be undone and may cause temporary service disruption.\n\n'
          'Are you sure you want to proceed?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _executeEmergencyRollback();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Emergency Rollback'),
          ),
        ],
      ),
    );
  }

  /// Execute emergency rollback
  Future<void> _executeEmergencyRollback() async {
    try {
      final service = ref.read(rollbackProceduresProvider);
      await service.triggerEmergencyRollback(
        'Manual emergency rollback triggered by developer',
        triggeredBy: 'developer',
      );
      
      // Refresh all providers
      ref.invalidate(systemHealthProvider);
      ref.invalidate(rollbackOperationsProvider);
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Emergency rollback initiated'),
          backgroundColor: Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to initiate emergency rollback: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Handle snapshot action
  void _handleSnapshotAction(String action, SystemSnapshot snapshot) {
    switch (action) {
      case 'rollback':
        _showRollbackConfirmationDialog(snapshot);
        break;
      case 'delete':
        _showDeleteSnapshotDialog(snapshot);
        break;
    }
  }

  /// Show rollback confirmation dialog
  void _showRollbackConfirmationDialog(SystemSnapshot snapshot) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Rollback'),
        content: Text(
          'Rollback system to snapshot "${snapshot.description}" created on ${_formatDateTime(snapshot.timestamp)}?\n\n'
          'This will restore the system to its previous state and may cause temporary disruption.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _executeRollbackToSnapshot(snapshot);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('Rollback'),
          ),
        ],
      ),
    );
  }

  /// Show delete snapshot dialog
  void _showDeleteSnapshotDialog(SystemSnapshot snapshot) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Snapshot'),
        content: Text('Delete snapshot "${snapshot.description}"?\n\nThis action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Handle snapshot deletion
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Snapshot deletion not implemented yet'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  /// Execute rollback to snapshot
  Future<void> _executeRollbackToSnapshot(SystemSnapshot snapshot) async {
    try {
      final service = ref.read(rollbackProceduresProvider);
      await service.triggerManualRollback(
        'Manual rollback to snapshot: ${snapshot.description}',
        scope: RollbackScope.fullSystem,
        targetSnapshotId: snapshot.id,
        triggeredBy: 'developer',
      );
      
      // Refresh all providers
      ref.invalidate(systemHealthProvider);
      ref.invalidate(rollbackOperationsProvider);
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Rollback initiated'),
          backgroundColor: Colors.orange,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to initiate rollback: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// Get status color
  Color _getStatusColor(RollbackStatus status) {
    switch (status) {
      case RollbackStatus.pending:
        return Colors.grey.shade600;
      case RollbackStatus.inProgress:
        return Colors.blue.shade600;
      case RollbackStatus.completed:
        return Colors.green.shade600;
      case RollbackStatus.failed:
        return Colors.red.shade600;
      case RollbackStatus.partiallyCompleted:
        return Colors.orange.shade600;
      case RollbackStatus.cancelled:
        return Colors.grey.shade600;
    }
  }

  /// Get status icon
  IconData _getStatusIcon(RollbackStatus status) {
    switch (status) {
      case RollbackStatus.pending:
        return Icons.schedule;
      case RollbackStatus.inProgress:
        return Icons.sync;
      case RollbackStatus.completed:
        return Icons.check_circle;
      case RollbackStatus.failed:
        return Icons.error;
      case RollbackStatus.partiallyCompleted:
        return Icons.warning;
      case RollbackStatus.cancelled:
        return Icons.cancel;
    }
  }

  /// Format date time
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${_formatTime(dateTime)}';
  }

  /// Format time
  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
