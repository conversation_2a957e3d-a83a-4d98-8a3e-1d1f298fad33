import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:integration_test/integration_test.dart';

import 'package:masajid_albahrain/core/settings/theme/theme_settings_provider.dart';
// Context7 MCP: Updated to use unified notification settings provider
import 'package:masajid_albahrain/core/notifications/providers/unified_notification_provider.dart';
import 'package:masajid_albahrain/core/settings/prayer_times/prayer_times_settings_provider.dart';
import 'package:masajid_albahrain/core/settings/location/location_settings_provider.dart';
import 'package:masajid_albahrain/core/settings/performance/performance_settings_provider.dart';
import 'package:masajid_albahrain/core/settings/advanced/advanced_settings_provider.dart';

/// Comprehensive integration testing suite for provider splitting implementation
/// Following Context7 MCP best practices for integration testing
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Provider Splitting Integration Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('All Domain Providers Initialize Successfully', (WidgetTester tester) async {
      // Test that all domain-specific providers can be initialized without errors
      expect(() => container.read(themeSettingsProvider), returnsNormally);
      expect(() => container.read(notificationSettingsProvider), returnsNormally);
      expect(() => container.read(prayerTimesSettingsProvider), returnsNormally);
      expect(() => container.read(locationSettingsProvider), returnsNormally);
      expect(() => container.read(performanceSettingsProvider), returnsNormally);
      expect(() => container.read(advancedSettingsProvider), returnsNormally);

      // Wait for all providers to load
      final themeState = await container.read(themeSettingsProvider.future);
      final notificationState = await container.read(notificationSettingsProvider.future);
      final prayerTimesState = await container.read(prayerTimesSettingsProvider.future);
      final locationState = await container.read(locationSettingsProvider.future);
      final performanceState = await container.read(performanceSettingsProvider.future);
      final advancedState = await container.read(advancedSettingsProvider.future);

      // Verify initial states are valid
      expect(themeState.themeMode, isNotNull);
      expect(notificationState.globalNotificationsEnabled, isNotNull);
      expect(prayerTimesState.calculationMethod, isNotNull);
      expect(locationState.autoLocation, isNotNull);
      expect(performanceState.enableAnimations, isNotNull);
      expect(advancedState.debugMode, isNotNull);

      print('✅ All domain providers initialized successfully');
    });

    testWidgets('Provider Independence Validation', (WidgetTester tester) async {
      int themeRebuildCount = 0;
      int notificationRebuildCount = 0;

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  // Theme consumer
                  Consumer(
                    builder: (context, ref, child) {
                      themeRebuildCount++;
                      final themeState = ref.watch(themeSettingsProvider);
                      return themeState.when(
                        data: (data) => Text('Theme: ${data.themeMode}'),
                        loading: () => const Text('Loading theme...'),
                        error: (_, __) => const Text('Theme error'),
                      );
                    },
                  ),
                  // Notification consumer
                  Consumer(
                    builder: (context, ref, child) {
                      notificationRebuildCount++;
                      final notificationState = ref.watch(notificationSettingsProvider);
                      return notificationState.when(
                        data: (data) => Text('Notifications: ${data.globalNotificationsEnabled}'),
                        loading: () => const Text('Loading notifications...'),
                        error: (_, __) => const Text('Notification error'),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Reset counters after initial build
      themeRebuildCount = 0;
      notificationRebuildCount = 0;

      // Update theme settings - should only trigger theme consumer rebuild
      await container.read(themeSettingsProvider.notifier).updateThemeMode(ThemeMode.dark);
      await tester.pump();

      expect(themeRebuildCount, equals(1), reason: 'Theme consumer should rebuild once');
      expect(notificationRebuildCount, equals(0), reason: 'Notification consumer should not rebuild');

      // Reset counters
      themeRebuildCount = 0;
      notificationRebuildCount = 0;

      // Update notification settings - should only trigger notification consumer rebuild
      await container.read(notificationSettingsProvider.notifier).updateGlobalNotifications(false);
      await tester.pump();

      expect(themeRebuildCount, equals(0), reason: 'Theme consumer should not rebuild');
      expect(notificationRebuildCount, equals(1), reason: 'Notification consumer should rebuild once');

      print('✅ Provider independence validated successfully');
    });

    testWidgets('State Persistence Validation', (WidgetTester tester) async {
      // Update various provider states
      await container.read(themeSettingsProvider.notifier).updateThemeMode(ThemeMode.dark);
      await container.read(notificationSettingsProvider.notifier).updateGlobalNotifications(false);

      // Wait for state updates to complete
      await tester.pump();

      // Verify states are updated correctly
      final themeState = await container.read(themeSettingsProvider.future);
      final notificationState = await container.read(notificationSettingsProvider.future);
      final prayerTimesState = await container.read(prayerTimesSettingsProvider.future);
      final locationState = await container.read(locationSettingsProvider.future);
      final performanceState = await container.read(performanceSettingsProvider.future);
      final advancedState = await container.read(advancedSettingsProvider.future);

      expect(themeState.themeMode, equals(ThemeMode.dark));
      expect(notificationState.globalNotificationsEnabled, equals(false));
      expect(prayerTimesState.calculationMethod, isNotNull);
      expect(locationState.autoLocation, isNotNull);
      expect(performanceState.enableAnimations, isNotNull);
      expect(advancedState.debugMode, isNotNull);

      print('✅ State persistence validated successfully');
    });

    testWidgets('Error Handling Validation', (WidgetTester tester) async {
      // Test error handling in providers
      try {
        // Test valid operations that should succeed
        await container.read(themeSettingsProvider.notifier).updateThemeMode(ThemeMode.light);
        await container.read(notificationSettingsProvider.notifier).updateGlobalNotifications(true);

        await tester.pump();

        // Verify states are still valid after operations
        final themeState = await container.read(themeSettingsProvider.future);
        final notificationState = await container.read(notificationSettingsProvider.future);

        expect(themeState.themeMode, equals(ThemeMode.light));
        expect(notificationState.globalNotificationsEnabled, equals(true));

        print('✅ Error handling validated successfully');
      } catch (e) {
        fail('Providers should handle valid inputs gracefully: $e');
      }
    });

    testWidgets('Concurrent Access Validation', (WidgetTester tester) async {
      // Test concurrent access to providers
      final futures = <Future>[];

      // Simulate concurrent updates from multiple sources
      for (int i = 0; i < 5; i++) {
        futures.add(
          Future(() async {
            await container
                .read(themeSettingsProvider.notifier)
                .updateThemeMode(i % 2 == 0 ? ThemeMode.dark : ThemeMode.light);
          }),
        );

        futures.add(
          Future(() async {
            await container.read(notificationSettingsProvider.notifier).updateGlobalNotifications(i % 2 == 0);
          }),
        );
      }

      // Wait for all concurrent operations to complete
      await Future.wait(futures);
      await tester.pump();

      // Verify providers are still in valid states
      final themeState = await container.read(themeSettingsProvider.future);
      final notificationState = await container.read(notificationSettingsProvider.future);
      final performanceState = await container.read(performanceSettingsProvider.future);

      expect(themeState.themeMode, isIn([ThemeMode.dark, ThemeMode.light]));
      expect(notificationState.globalNotificationsEnabled, isA<bool>());
      expect(performanceState.enableAnimations, isA<bool>());

      print('✅ Concurrent access validated successfully');
    });

    testWidgets('Memory Leak Prevention Validation', (WidgetTester tester) async {
      // Create and dispose multiple provider containers to test for memory leaks
      final containers = <ProviderContainer>[];

      for (int i = 0; i < 10; i++) {
        final testContainer = ProviderContainer();

        // Initialize all providers
        testContainer.read(themeSettingsProvider);
        testContainer.read(notificationSettingsProvider);
        testContainer.read(prayerTimesSettingsProvider);
        testContainer.read(locationSettingsProvider);
        testContainer.read(performanceSettingsProvider);
        testContainer.read(advancedSettingsProvider);

        containers.add(testContainer);
      }

      // Dispose all containers
      for (final testContainer in containers) {
        testContainer.dispose();
      }

      // Force garbage collection
      await tester.binding.delayed(const Duration(milliseconds: 100));

      // Verify main container still works correctly
      final themeState = await container.read(themeSettingsProvider.future);
      expect(themeState, isNotNull);

      print('✅ Memory leak prevention validated successfully');
    });

    testWidgets('Migration Compatibility Validation', (WidgetTester tester) async {
      // Test that new providers work alongside legacy compatibility layer
      // This ensures smooth migration path

      // Update settings using new providers
      await container.read(themeSettingsProvider.notifier).updateThemeMode(ThemeMode.dark);
      await container.read(notificationSettingsProvider.notifier).updateGlobalNotifications(true);

      await tester.pump();

      // Verify states are accessible and consistent
      final themeState = await container.read(themeSettingsProvider.future);
      final notificationState = await container.read(notificationSettingsProvider.future);

      expect(themeState.themeMode, equals(ThemeMode.dark));
      expect(notificationState.globalNotificationsEnabled, equals(true));

      print('✅ Migration compatibility validated successfully');
    });
  });
}
