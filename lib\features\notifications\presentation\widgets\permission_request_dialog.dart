import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/utils/logger.dart';
// Context7 MCP: Integrated with unified notification provider for permission handling

/// Notification permission request dialog for Android 13+ compliance.
///
/// This dialog provides a user-friendly way to request notification permissions
/// that complies with Android 13+ requirements where permission requests must
/// be triggered by explicit user interaction, not automatic app initialization.
///
/// Features:
/// - Clear explanation of why notifications are needed
/// - User-friendly design with icon and proper messaging
/// - Handles both "Allow" and "Not Now" responses
/// - Returns boolean result for permission decision
/// - Context7 MCP: Integrated with unified notification provider
class NotificationPermissionDialog extends ConsumerStatefulWidget {
  /// Creates a notification permission request dialog.
  const NotificationPermissionDialog({super.key});

  @override
  ConsumerState<NotificationPermissionDialog> createState() => _NotificationPermissionDialogState();
}

class _NotificationPermissionDialogState extends ConsumerState<NotificationPermissionDialog> {
  bool _isRequesting = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      icon: const Icon(Icons.notifications_outlined, size: 48, color: Colors.blue),
      title: const Text('Enable Prayer Notifications', style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
      content: const Text(
        'Get notified before each prayer time to stay connected with your prayers.\n\n'
        'You can change this setting anytime in your device settings.',
        style: TextStyle(fontSize: 16),
      ),
      actions: [
        TextButton(
          onPressed: _isRequesting ? null : () => Navigator.of(context).pop(false),
          child: const Text('Not Now', style: TextStyle(color: Colors.grey)),
        ),
        ElevatedButton(
          onPressed: _isRequesting ? null : _handleEnablePressed,
          style: ElevatedButton.styleFrom(backgroundColor: Colors.blue, foregroundColor: Colors.white),
          child: _isRequesting
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                )
              : const Text('Enable'),
        ),
      ],
    );
  }

  /// Context7 MCP: Handle enable button press with unified provider
  Future<void> _handleEnablePressed() async {
    setState(() {
      _isRequesting = true;
    });

    try {
      AppLogger.debug('NotificationPermissionDialog: Requesting notification permissions');

      // Context7 MCP: Simple permission request - detailed handling is done by the unified provider
      // This dialog just indicates user intent, actual permission handling is managed elsewhere
      AppLogger.info('NotificationPermissionDialog: User agreed to enable notifications');
      if (mounted) {
        Navigator.of(context).pop(true);
      }
    } on Exception catch (error) {
      AppLogger.error('NotificationPermissionDialog: Error requesting permissions - $error');
      if (mounted) {
        Navigator.of(context).pop(false);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRequesting = false;
        });
      }
    }
  }
}
