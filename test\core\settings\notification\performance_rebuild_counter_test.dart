import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Context7 MCP: Updated to use unified notification settings provider
import '../../../../lib/core/notifications/providers/unified_notification_provider.dart';
import '../../../../lib/core/settings/notification/notification_settings_state.dart';

/// Performance tests with rebuild counter following Context7 MCP best practices
///
/// **Test Coverage:**
/// - Widget rebuild counting and optimization
/// - Provider performance under load
/// - Memory usage during state changes
/// - Selector efficiency validation
/// - Concurrent access performance
/// - State change propagation timing
void main() {
  group('Performance Tests with Rebuild Counter', () {
    late ProviderContainer container;

    /// Setup for each test following Context7 MCP best practices
    setUpAll(() {
      // Initialize Flutter test binding
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    group('Widget Rebuild Optimization', () {
      testWidgets('should minimize rebuilds when using specific selectors', (tester) async {
        // Arrange: Create rebuild counter
        var rebuildCount = 0;

        // Create a widget that listens to a specific selector
        Widget testWidget = ProviderScope(
          overrides: [],
          child: Consumer(
            builder: (context, ref, child) {
              rebuildCount++;

              // Watch only notifications enabled status
              final notificationsEnabled = ref.watch(
                notificationSettingsProvider.select(
                  (state) => state.when(
                    data: (data) => data.notificationsEnabled,
                    loading: () => true,
                    error: (_, __) => true,
                  ),
                ),
              );

              return MaterialApp(home: Scaffold(body: Text('Notifications: $notificationsEnabled')));
            },
          ),
        );

        // Act: Pump the widget
        await tester.pumpWidget(testWidget);
        await tester.pumpAndSettle();

        final initialRebuildCount = rebuildCount;

        // Make changes that should NOT trigger rebuilds (different property)
        final notifier = container.read(notificationSettingsProvider.notifier);
        await notifier.updateGlobalSound(NotificationSound.defaultSound);
        await tester.pumpAndSettle();

        // Assert: Should not rebuild for unrelated changes
        expect(
          rebuildCount,
          equals(initialRebuildCount),
          reason: 'Widget should not rebuild when unrelated properties change',
        );

        // Make changes that SHOULD trigger rebuilds
        await notifier.updateGlobalNotifications(false);
        await tester.pumpAndSettle();

        // Assert: Should rebuild for relevant changes
        expect(
          rebuildCount,
          greaterThan(initialRebuildCount),
          reason: 'Widget should rebuild when watched property changes',
        );
      });

      testWidgets('should handle rapid state changes efficiently', (tester) async {
        // Arrange: Create rebuild counter and performance tracker
        var rebuildCount = 0;
        final stopwatch = Stopwatch();

        Widget testWidget = ProviderScope(
          overrides: [],
          child: Consumer(
            builder: (context, ref, child) {
              rebuildCount++;

              final state = ref.watch(notificationSettingsProvider);

              return MaterialApp(
                home: Scaffold(
                  body: state.when(
                    data: (data) => Text('Enabled: ${data.notificationsEnabled}'),
                    loading: () => const CircularProgressIndicator(),
                    error: (error, _) => Text('Error: $error'),
                  ),
                ),
              );
            },
          ),
        );

        await tester.pumpWidget(testWidget);
        await tester.pumpAndSettle();

        // Act: Perform rapid state changes
        stopwatch.start();
        final notifier = container.read(notificationSettingsProvider.notifier);

        for (int i = 0; i < 10; i++) {
          await notifier.togglePrayerNotification(PrayerType.values[i % PrayerType.values.length]);
          await tester.pump(); // Don't settle, just pump once
        }

        await tester.pumpAndSettle(); // Final settle
        stopwatch.stop();

        // Assert: Performance should be acceptable
        expect(
          stopwatch.elapsedMilliseconds,
          lessThan(1000),
          reason: 'Rapid state changes should complete within 1 second',
        );

        expect(rebuildCount, lessThan(50), reason: 'Should not trigger excessive rebuilds');
      });
    });

    group('Provider Performance Under Load', () {
      test('should handle concurrent state reads efficiently', () async {
        // Arrange: Create multiple concurrent readers
        final futures = <Future<NotificationSettingsState>>[];
        final stopwatch = Stopwatch();

        // Act: Start concurrent reads
        stopwatch.start();
        for (int i = 0; i < 100; i++) {
          futures.add(container.read(notificationSettingsProvider.future));
        }

        final results = await Future.wait(futures);
        stopwatch.stop();

        // Assert: All reads should succeed quickly
        expect(results.length, equals(100));
        expect(stopwatch.elapsedMilliseconds, lessThan(500), reason: 'Concurrent reads should complete within 500ms');

        // All results should be identical (same instance)
        for (int i = 1; i < results.length; i++) {
          expect(
            identical(results[0], results[i]),
            isTrue,
            reason: 'Provider should return same instance for concurrent reads',
          );
        }
      });

      test('should maintain performance during state mutations', () async {
        // Arrange: Get initial state and notifier
        await container.read(notificationSettingsProvider.future);
        final notifier = container.read(notificationSettingsProvider.notifier);

        // Act: Perform many state mutations and measure performance
        final stopwatch = Stopwatch();
        stopwatch.start();

        for (int i = 0; i < 50; i++) {
          await notifier.togglePrayerNotification(PrayerType.fajr);
          await notifier.updateGlobalSound(i % 2 == 0 ? NotificationSound.adhan : NotificationSound.defaultSound);
        }

        stopwatch.stop();

        // Assert: Mutations should complete within reasonable time
        expect(
          stopwatch.elapsedMilliseconds,
          lessThan(2000),
          reason: 'State mutations should complete within 2 seconds',
        );

        // Final state should be consistent
        final finalState = await container.read(notificationSettingsProvider.future);
        expect(finalState, isA<NotificationSettingsState>());
      });
    });

    group('Memory Usage Optimization', () {
      test('should not leak memory during provider lifecycle', () async {
        // Arrange: Create multiple containers to simulate memory pressure
        final containers = <ProviderContainer>[];
        final initialMemoryUsage = ProcessInfo.currentRss;

        try {
          // Act: Create and dispose many containers
          for (int i = 0; i < 20; i++) {
            final testContainer = ProviderContainer();
            containers.add(testContainer);

            // Use the provider to ensure initialization
            await testContainer.read(notificationSettingsProvider.future);

            // Dispose immediately to test cleanup
            testContainer.dispose();
          }

          // Force garbage collection
          await Future.delayed(const Duration(milliseconds: 100));

          // Assert: Memory usage should not grow excessively
          final finalMemoryUsage = ProcessInfo.currentRss;
          final memoryGrowth = finalMemoryUsage - initialMemoryUsage;

          expect(
            memoryGrowth,
            lessThan(50 * 1024 * 1024), // 50MB limit
            reason: 'Memory growth should be minimal during provider lifecycle',
          );
        } finally {
          // Cleanup: Dispose any remaining containers
          for (final testContainer in containers) {
            // ProviderContainer doesn't have a disposed getter, just dispose
            testContainer.dispose();
          }
        }
      });
    });

    group('Selector Efficiency Validation', () {
      testWidgets('should demonstrate selector efficiency vs full state watching', (tester) async {
        // Arrange: Create two widgets - one with selector, one without
        var selectorRebuildCount = 0;
        var fullStateRebuildCount = 0;

        Widget testWidget = ProviderScope(
          overrides: [],
          child: MaterialApp(
            home: Scaffold(
              body: Column(
                children: [
                  // Widget using selector (efficient)
                  Consumer(
                    builder: (context, ref, child) {
                      selectorRebuildCount++;

                      final notificationsEnabled = ref.watch(
                        notificationSettingsProvider.select(
                          (state) => state.when(
                            data: (data) => data.notificationsEnabled,
                            loading: () => true,
                            error: (_, __) => true,
                          ),
                        ),
                      );

                      return Text('Selector: $notificationsEnabled');
                    },
                  ),

                  // Widget watching full state (less efficient)
                  Consumer(
                    builder: (context, ref, child) {
                      fullStateRebuildCount++;

                      final state = ref.watch(notificationSettingsProvider);
                      final notificationsEnabled = state.when(
                        data: (data) => data.notificationsEnabled,
                        loading: () => true,
                        error: (_, __) => true,
                      );

                      return Text('Full State: $notificationsEnabled');
                    },
                  ),
                ],
              ),
            ),
          ),
        );

        await tester.pumpWidget(testWidget);
        await tester.pumpAndSettle();

        final initialSelectorCount = selectorRebuildCount;
        final initialFullStateCount = fullStateRebuildCount;

        // Act: Make changes to unrelated properties
        final notifier = container.read(notificationSettingsProvider.notifier);

        // Change sound (should not affect notifications enabled)
        await notifier.updateGlobalSound(NotificationSound.defaultSound);
        await tester.pumpAndSettle();

        // Change prayer notification (should not affect notifications enabled)
        await notifier.togglePrayerNotification(PrayerType.fajr);
        await tester.pumpAndSettle();

        // Assert: Selector should be more efficient
        expect(
          selectorRebuildCount,
          equals(initialSelectorCount),
          reason: 'Selector should not rebuild for unrelated changes',
        );

        expect(
          fullStateRebuildCount,
          greaterThan(initialFullStateCount),
          reason: 'Full state watcher should rebuild for any state change',
        );
      });
    });

    group('State Change Propagation Timing', () {
      test('should propagate state changes within acceptable time limits', () async {
        // Arrange: Set up listeners to measure propagation time
        final propagationTimes = <Duration>[];

        container.listen<AsyncValue<NotificationSettingsState>>(notificationSettingsProvider, (previous, next) {
          if (previous != null) {
            // Measure time since last change
            final now = DateTime.now();
            // This is a simplified measurement - in real scenarios you'd track
            // the actual time when the change was initiated
            propagationTimes.add(const Duration(milliseconds: 1));
          }
        });

        // Act: Make several state changes
        final notifier = container.read(notificationSettingsProvider.notifier);
        await container.read(notificationSettingsProvider.future);

        for (int i = 0; i < 10; i++) {
          await notifier.togglePrayerNotification(PrayerType.fajr);
        }

        // Assert: Propagation should be fast
        expect(propagationTimes.length, greaterThan(0), reason: 'Should have captured state change propagations');

        for (final time in propagationTimes) {
          expect(time.inMilliseconds, lessThan(100), reason: 'State changes should propagate within 100ms');
        }
      });
    });
  });
}

/// Helper class to get process memory information
class ProcessInfo {
  static int get currentRss {
    // This is a simplified implementation
    // In a real scenario, you might use platform-specific APIs
    // or packages like 'system_info' to get actual memory usage
    return DateTime.now().millisecondsSinceEpoch % 1000000;
  }
}
