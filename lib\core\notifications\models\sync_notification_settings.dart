import 'package:flutter/foundation.dart';

/// Sync Notification Settings
///
/// Comprehensive sync notification settings model following Context7 MCP best practices
/// for managing background sync notification preferences and behaviors.
///
/// This model provides granular control over sync notifications including
/// when to show notifications, progress update thresholds, and auto-dismissal settings.
@immutable
class SyncNotificationSettings {
  /// Whether to show notifications when sync operations start
  final bool showStartNotifications;

  /// Whether to show progress update notifications
  final bool showProgressNotifications;

  /// Whether to show completion notifications
  final bool showCompletionNotifications;

  /// Whether to show error notifications
  final bool showErrorNotifications;

  /// Whether to automatically show notifications from progress tracking
  final bool autoShowFromProgressTracking;

  /// Progress update threshold (percentage change required to update)
  final int progressUpdateThreshold;

  /// Minimum interval between progress updates
  final Duration minUpdateInterval;

  /// Whether to auto-dismiss completion notifications
  final bool autoDismissCompletion;

  /// Delay before auto-dismissing completion notifications
  final Duration autoDismissCompletionDelay;

  /// Whether to auto-dismiss notifications when service is disposed
  final bool autoDismissOnDispose;

  /// Whether to group sync notifications
  final bool groupNotifications;

  /// Maximum number of active sync notifications
  final int maxActiveNotifications;

  /// Whether to show notifications only for long operations
  final bool onlyForLongOperations;

  /// Minimum operation duration to show notifications
  final Duration minOperationDuration;

  /// Whether to show detailed progress information
  final bool showDetailedProgress;

  /// Whether to show operation timing information
  final bool showTimingInfo;

  /// Whether to enable notification sounds for sync operations
  final bool enableSounds;

  /// Whether to enable vibration for sync notifications
  final bool enableVibration;

  /// Notification priority level
  final SyncNotificationPriority priority;

  /// Creates sync notification settings with the specified configuration
  const SyncNotificationSettings({
    this.showStartNotifications = false,
    this.showProgressNotifications = true,
    this.showCompletionNotifications = true,
    this.showErrorNotifications = true,
    this.autoShowFromProgressTracking = true,
    this.progressUpdateThreshold = 10,
    this.minUpdateInterval = const Duration(seconds: 2),
    this.autoDismissCompletion = true,
    this.autoDismissCompletionDelay = const Duration(seconds: 5),
    this.autoDismissOnDispose = false,
    this.groupNotifications = true,
    this.maxActiveNotifications = 5,
    this.onlyForLongOperations = false,
    this.minOperationDuration = const Duration(seconds: 10),
    this.showDetailedProgress = true,
    this.showTimingInfo = false,
    this.enableSounds = false,
    this.enableVibration = false,
    this.priority = SyncNotificationPriority.low,
  });

  /// Create default sync notification settings
  factory SyncNotificationSettings.defaultSettings() {
    return const SyncNotificationSettings(
      showStartNotifications: false,
      showProgressNotifications: true,
      showCompletionNotifications: true,
      showErrorNotifications: true,
      autoShowFromProgressTracking: true,
      progressUpdateThreshold: 10,
      minUpdateInterval: Duration(seconds: 2),
      autoDismissCompletion: true,
      autoDismissCompletionDelay: Duration(seconds: 5),
      autoDismissOnDispose: false,
      groupNotifications: true,
      maxActiveNotifications: 5,
      onlyForLongOperations: false,
      minOperationDuration: Duration(seconds: 10),
      showDetailedProgress: true,
      showTimingInfo: false,
      enableSounds: false,
      enableVibration: false,
      priority: SyncNotificationPriority.low,
    );
  }

  /// Create settings optimized for minimal notifications
  factory SyncNotificationSettings.minimal() {
    return const SyncNotificationSettings(
      showStartNotifications: false,
      showProgressNotifications: false,
      showCompletionNotifications: false,
      showErrorNotifications: true,
      autoShowFromProgressTracking: false,
      progressUpdateThreshold: 25,
      minUpdateInterval: Duration(seconds: 5),
      autoDismissCompletion: true,
      autoDismissCompletionDelay: Duration(seconds: 3),
      autoDismissOnDispose: true,
      groupNotifications: true,
      maxActiveNotifications: 2,
      onlyForLongOperations: true,
      minOperationDuration: Duration(minutes: 1),
      showDetailedProgress: false,
      showTimingInfo: false,
      enableSounds: false,
      enableVibration: false,
      priority: SyncNotificationPriority.min,
    );
  }

  /// Create settings optimized for detailed notifications
  factory SyncNotificationSettings.detailed() {
    return const SyncNotificationSettings(
      showStartNotifications: true,
      showProgressNotifications: true,
      showCompletionNotifications: true,
      showErrorNotifications: true,
      autoShowFromProgressTracking: true,
      progressUpdateThreshold: 5,
      minUpdateInterval: Duration(seconds: 1),
      autoDismissCompletion: false,
      autoDismissCompletionDelay: Duration(seconds: 10),
      autoDismissOnDispose: false,
      groupNotifications: false,
      maxActiveNotifications: 10,
      onlyForLongOperations: false,
      minOperationDuration: Duration(seconds: 1),
      showDetailedProgress: true,
      showTimingInfo: true,
      enableSounds: true,
      enableVibration: true,
      priority: SyncNotificationPriority.normal,
    );
  }

  /// Create a copy with updated properties
  SyncNotificationSettings copyWith({
    bool? showStartNotifications,
    bool? showProgressNotifications,
    bool? showCompletionNotifications,
    bool? showErrorNotifications,
    bool? autoShowFromProgressTracking,
    int? progressUpdateThreshold,
    Duration? minUpdateInterval,
    bool? autoDismissCompletion,
    Duration? autoDismissCompletionDelay,
    bool? autoDismissOnDispose,
    bool? groupNotifications,
    int? maxActiveNotifications,
    bool? onlyForLongOperations,
    Duration? minOperationDuration,
    bool? showDetailedProgress,
    bool? showTimingInfo,
    bool? enableSounds,
    bool? enableVibration,
    SyncNotificationPriority? priority,
  }) {
    return SyncNotificationSettings(
      showStartNotifications: showStartNotifications ?? this.showStartNotifications,
      showProgressNotifications: showProgressNotifications ?? this.showProgressNotifications,
      showCompletionNotifications: showCompletionNotifications ?? this.showCompletionNotifications,
      showErrorNotifications: showErrorNotifications ?? this.showErrorNotifications,
      autoShowFromProgressTracking: autoShowFromProgressTracking ?? this.autoShowFromProgressTracking,
      progressUpdateThreshold: progressUpdateThreshold ?? this.progressUpdateThreshold,
      minUpdateInterval: minUpdateInterval ?? this.minUpdateInterval,
      autoDismissCompletion: autoDismissCompletion ?? this.autoDismissCompletion,
      autoDismissCompletionDelay: autoDismissCompletionDelay ?? this.autoDismissCompletionDelay,
      autoDismissOnDispose: autoDismissOnDispose ?? this.autoDismissOnDispose,
      groupNotifications: groupNotifications ?? this.groupNotifications,
      maxActiveNotifications: maxActiveNotifications ?? this.maxActiveNotifications,
      onlyForLongOperations: onlyForLongOperations ?? this.onlyForLongOperations,
      minOperationDuration: minOperationDuration ?? this.minOperationDuration,
      showDetailedProgress: showDetailedProgress ?? this.showDetailedProgress,
      showTimingInfo: showTimingInfo ?? this.showTimingInfo,
      enableSounds: enableSounds ?? this.enableSounds,
      enableVibration: enableVibration ?? this.enableVibration,
      priority: priority ?? this.priority,
    );
  }

  /// Check if notifications should be shown for an operation duration
  bool shouldShowForDuration(Duration operationDuration) {
    if (!onlyForLongOperations) return true;
    return operationDuration >= minOperationDuration;
  }

  /// Get effective notification priority
  SyncNotificationPriority getEffectivePriority() {
    return priority;
  }

  /// Convert to JSON representation
  Map<String, dynamic> toJson() {
    return {
      'showStartNotifications': showStartNotifications,
      'showProgressNotifications': showProgressNotifications,
      'showCompletionNotifications': showCompletionNotifications,
      'showErrorNotifications': showErrorNotifications,
      'autoShowFromProgressTracking': autoShowFromProgressTracking,
      'progressUpdateThreshold': progressUpdateThreshold,
      'minUpdateIntervalMs': minUpdateInterval.inMilliseconds,
      'autoDismissCompletion': autoDismissCompletion,
      'autoDismissCompletionDelayMs': autoDismissCompletionDelay.inMilliseconds,
      'autoDismissOnDispose': autoDismissOnDispose,
      'groupNotifications': groupNotifications,
      'maxActiveNotifications': maxActiveNotifications,
      'onlyForLongOperations': onlyForLongOperations,
      'minOperationDurationMs': minOperationDuration.inMilliseconds,
      'showDetailedProgress': showDetailedProgress,
      'showTimingInfo': showTimingInfo,
      'enableSounds': enableSounds,
      'enableVibration': enableVibration,
      'priority': priority.name,
    };
  }

  /// Validate settings consistency and correctness
  ///
  /// Context7 MCP: Comprehensive validation following defensive programming principles
  bool isValid() {
    try {
      // Validate progress update threshold
      if (progressUpdateThreshold < 1 || progressUpdateThreshold > 100) {
        return false; // Invalid threshold (1-100%)
      }

      // Validate minimum update interval
      if (minUpdateInterval.inSeconds < 1 || minUpdateInterval.inSeconds > 3600) {
        return false; // Invalid interval (1 second to 1 hour)
      }

      // Validate auto-dismiss delay
      if (autoDismissCompletionDelay.inSeconds < 1 || autoDismissCompletionDelay.inSeconds > 300) {
        return false; // Invalid delay (1 second to 5 minutes)
      }

      // Validate max active notifications
      if (maxActiveNotifications < 1 || maxActiveNotifications > 20) {
        return false; // Invalid count (1-20)
      }

      // Validate minimum operation duration
      if (minOperationDuration.inSeconds < 1 || minOperationDuration.inSeconds > 3600) {
        return false; // Invalid duration (1 second to 1 hour)
      }

      return true;
    } on Exception {
      return false;
    }
  }

  /// Create from JSON representation
  factory SyncNotificationSettings.fromJson(Map<String, dynamic> json) {
    return SyncNotificationSettings(
      showStartNotifications: json['showStartNotifications'] as bool? ?? false,
      showProgressNotifications: json['showProgressNotifications'] as bool? ?? true,
      showCompletionNotifications: json['showCompletionNotifications'] as bool? ?? true,
      showErrorNotifications: json['showErrorNotifications'] as bool? ?? true,
      autoShowFromProgressTracking: json['autoShowFromProgressTracking'] as bool? ?? true,
      progressUpdateThreshold: json['progressUpdateThreshold'] as int? ?? 10,
      minUpdateInterval: Duration(milliseconds: json['minUpdateIntervalMs'] as int? ?? 2000),
      autoDismissCompletion: json['autoDismissCompletion'] as bool? ?? true,
      autoDismissCompletionDelay: Duration(milliseconds: json['autoDismissCompletionDelayMs'] as int? ?? 5000),
      autoDismissOnDispose: json['autoDismissOnDispose'] as bool? ?? false,
      groupNotifications: json['groupNotifications'] as bool? ?? true,
      maxActiveNotifications: json['maxActiveNotifications'] as int? ?? 5,
      onlyForLongOperations: json['onlyForLongOperations'] as bool? ?? false,
      minOperationDuration: Duration(milliseconds: json['minOperationDurationMs'] as int? ?? 10000),
      showDetailedProgress: json['showDetailedProgress'] as bool? ?? true,
      showTimingInfo: json['showTimingInfo'] as bool? ?? false,
      enableSounds: json['enableSounds'] as bool? ?? false,
      enableVibration: json['enableVibration'] as bool? ?? false,
      priority: SyncNotificationPriority.values.firstWhere(
        (e) => e.name == json['priority'],
        orElse: () => SyncNotificationPriority.low,
      ),
    );
  }

  @override
  String toString() {
    return 'SyncNotificationSettings(progress: $showProgressNotifications, completion: $showCompletionNotifications, errors: $showErrorNotifications)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SyncNotificationSettings &&
        other.showStartNotifications == showStartNotifications &&
        other.showProgressNotifications == showProgressNotifications &&
        other.showCompletionNotifications == showCompletionNotifications &&
        other.showErrorNotifications == showErrorNotifications &&
        other.autoShowFromProgressTracking == autoShowFromProgressTracking &&
        other.progressUpdateThreshold == progressUpdateThreshold &&
        other.minUpdateInterval == minUpdateInterval &&
        other.autoDismissCompletion == autoDismissCompletion &&
        other.autoDismissCompletionDelay == autoDismissCompletionDelay &&
        other.autoDismissOnDispose == autoDismissOnDispose &&
        other.groupNotifications == groupNotifications &&
        other.maxActiveNotifications == maxActiveNotifications &&
        other.onlyForLongOperations == onlyForLongOperations &&
        other.minOperationDuration == minOperationDuration &&
        other.showDetailedProgress == showDetailedProgress &&
        other.showTimingInfo == showTimingInfo &&
        other.enableSounds == enableSounds &&
        other.enableVibration == enableVibration &&
        other.priority == priority;
  }

  @override
  int get hashCode {
    return Object.hash(
      showStartNotifications,
      showProgressNotifications,
      showCompletionNotifications,
      showErrorNotifications,
      autoShowFromProgressTracking,
      progressUpdateThreshold,
      minUpdateInterval,
      autoDismissCompletion,
      autoDismissCompletionDelay,
      autoDismissOnDispose,
      groupNotifications,
      maxActiveNotifications,
      onlyForLongOperations,
      minOperationDuration,
      showDetailedProgress,
      showTimingInfo,
      enableSounds,
      enableVibration,
      priority,
    );
  }
}

/// Sync Notification Priority Levels
///
/// Defines the priority levels for sync notifications affecting their display behavior.
enum SyncNotificationPriority {
  /// Minimum priority - may not be shown
  min,

  /// Low priority - shown in collapsed form
  low,

  /// Normal priority - default behavior
  normal,

  /// High priority - shown expanded
  high,
}
