import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../features/home/<USER>/repositories/masjid_repository.dart';
import '../../security/network/certificate_pinning_service.dart';
import '../../security/network/secure_http_client.dart';
import '../../security/secure_key_manager.dart';
import '../../interfaces/unified_cache_service_interface.dart';
import '../../interfaces/unified_database_service_interface.dart';
import '../enhanced_auth_service.dart';
import '../implementations/auth_service_impl.dart';
import '../implementations/database_service_impl.dart';
import '../implementations/location_service_impl.dart';
import '../implementations/notification_service_impl.dart';
import '../implementations/prayer_times_service_impl.dart';
import '../interfaces/auth_service_interface.dart';
import '../interfaces/database_service_interface.dart';
import '../unified_database_service.dart';
import '../interfaces/location_service_interface.dart';
import '../interfaces/notification_service_interface.dart';
import '../interfaces/prayer_times_service_interface.dart';

/// Infrastructure Service Providers
/// These services provide core functionality that other services depend on

final secureKeyManagerProvider = Provider<SecureKeyManager>((ref) {
  return SecureKeyManager.instance;
});

final certificatePinningServiceProvider = Provider<CertificatePinningService>((ref) {
  return CertificatePinningService.instance;
});

final secureHttpClientProvider = Provider<SecureHttpClient>((ref) {
  return SecureHttpClient.instance;
});

final cacheServiceProvider = Provider<CacheService>((ref) {
  return CacheService();
});

/// Core Service Providers with Dependency Injection

final authServiceProvider = Provider<AuthServiceInterface>((ref) {
  final secureKeyManager = ref.watch(secureKeyManagerProvider);
  final secureHttpClient = ref.watch(secureHttpClientProvider);

  return AuthServiceImpl(secureKeyManager: secureKeyManager, secureHttpClient: secureHttpClient);
});

/// Unified Database Service Provider
///
/// Provides the new consolidated database service that replaces multiple
/// legacy services with a single, comprehensive implementation following
/// Context7 MCP best practices.
///
/// Features:
/// - Intelligent caching with automatic invalidation
/// - Real-time health monitoring and connection management
/// - Performance metrics collection and analysis
/// - Retry mechanisms with exponential backoff
/// - Type-safe query operations with Result pattern
/// - Batch operations with transaction support
/// - Real-time subscriptions with automatic reconnection
final unifiedDatabaseServiceProvider = Provider<UnifiedDatabaseServiceInterface>((ref) {
  return UnifiedDatabaseService();
});

/// Legacy Database Service Provider (for backward compatibility)
///
/// This provider maintains compatibility with existing code during the
/// migration period. It now delegates operations to the unified service
/// while maintaining the original interface.
final databaseServiceProvider = Provider<DatabaseServiceInterface>((ref) {
  final unifiedService = ref.watch(unifiedDatabaseServiceProvider);
  final cacheService = ref.watch(cacheServiceProvider);
  final secureHttpClient = ref.watch(secureHttpClientProvider);

  return DatabaseServiceImpl(
    cacheService: cacheService,
    secureHttpClient: secureHttpClient,
    unifiedDatabaseService: unifiedService,
  );
});

final locationServiceProvider = Provider<LocationServiceInterface>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);
  final cacheService = ref.watch(cacheServiceProvider);

  return LocationServiceImpl(databaseService: databaseService, cacheService: cacheService);
});

// Note: notificationServiceProvider is now defined as a @riverpod provider
// in lib/core/notifications/providers/unified_notification_provider.dart

// Note: prayerTimesServiceProvider is now defined as a @riverpod provider
// in lib/features/prayer_times/presentation/providers/prayer_times_provider.dart

/// Legacy Service Providers (for backward compatibility during migration)

final legacyAuthServiceProvider = Provider<EnhancedAuthService>((ref) {
  return EnhancedAuthService();
});

/// Repository Providers (Business Logic Layer)

final masjidRepositoryProvider = Provider<MasjidRepository>((ref) {
  final databaseService = ref.watch(databaseServiceProvider);

  return MasjidRepository(databaseService: databaseService);
});

/// Service Manager Provider
/// Manages the lifecycle of all services

final serviceManagerProvider = Provider<ServiceManager>((ref) {
  return ServiceManager(ref);
});

/// Service Manager Class
/// Handles initialization, disposal, and health monitoring of all services
class ServiceManager {
  ServiceManager(this._ref);

  final Ref _ref;
  final Map<String, bool> _serviceStates = {};
  bool _isInitialized = false;

  /// Initialize all services in the correct order
  Future<void> initializeServices() async {
    if (_isInitialized) return;

    try {
      // Phase 1: Infrastructure Services (in priority order)
      await _initializeInfrastructureServices();

      // Phase 2: Core Services
      await _initializeCoreServices();

      // Phase 3: Business Services
      await _initializeBusinessServices();

      _isInitialized = true;
    } catch (e) {
      throw ServiceInitializationException('Failed to initialize services: $e');
    }
  }

  /// Initialize infrastructure services
  Future<void> _initializeInfrastructureServices() async {
    // Secure Key Manager (highest priority)
    final secureKeyManager = _ref.read(secureKeyManagerProvider);
    await _initializeService('SecureKeyManager', () async {
      await secureKeyManager.initialize();
    });

    // Certificate Pinning Service
    final certificatePinningService = _ref.read(certificatePinningServiceProvider);
    await _initializeService('CertificatePinningService', () async {
      await certificatePinningService.initialize();
    });

    // Secure HTTP Client
    final secureHttpClient = _ref.read(secureHttpClientProvider);
    await _initializeService('SecureHttpClient', () async {
      await secureHttpClient.initialize();
    });

    // Cache Service
    final cacheService = _ref.read(cacheServiceProvider);
    await _initializeService('CacheService', () async {
      await cacheService.init();
    });

    // Unified Database Service (new consolidated service)
    final unifiedDatabaseService = _ref.read(unifiedDatabaseServiceProvider);
    await _initializeService('UnifiedDatabaseService', () async {
      await unifiedDatabaseService.initialize();
    });
  }

  /// Initialize core services
  Future<void> _initializeCoreServices() async {
    // Auth Service
    final authService = _ref.read(authServiceProvider);
    await _initializeService('AuthService', () async {
      await authService.initialize();
    });

    // Database Service (legacy compatibility layer)
    final databaseService = _ref.read(databaseServiceProvider);
    await _initializeService('DatabaseService', () async {
      await databaseService.initialize();
    });

    // Location Service
    final locationService = _ref.read(locationServiceProvider);
    await _initializeService('LocationService', () async {
      await locationService.initialize();
    });

    // Notification Service
    final notificationService = _ref.read(notificationServiceProvider);
    await _initializeService('NotificationService', () async {
      await notificationService.initialize();
    });
  }

  /// Initialize business services
  Future<void> _initializeBusinessServices() async {
    // Prayer Times Service
    final prayerTimesService = _ref.read(prayerTimesServiceProvider);
    await _initializeService('PrayerTimesService', () async {
      await prayerTimesService.initialize();
    });
  }

  /// Initialize a single service with error handling
  Future<void> _initializeService(String serviceName, Future<void> Function() initializer) async {
    try {
      await initializer();
      _serviceStates[serviceName] = true;
    } catch (e) {
      _serviceStates[serviceName] = false;
      throw ServiceInitializationException('Failed to initialize $serviceName: $e');
    }
  }

  /// Dispose all services
  Future<void> disposeServices() async {
    if (!_isInitialized) return;

    // Dispose in reverse order
    final services = [
      'PrayerTimesService',
      'NotificationService',
      'LocationService',
      'DatabaseService',
      'AuthService',
      'UnifiedDatabaseService',
      'CacheService',
      'SecureHttpClient',
      'CertificatePinningService',
      'SecureKeyManager',
    ];

    for (final serviceName in services) {
      try {
        await _disposeService(serviceName);
      } catch (e) {
        // Log error but continue disposing other services
        print('Error disposing $serviceName: $e');
      }
    }

    _isInitialized = false;
    _serviceStates.clear();
  }

  /// Dispose a single service
  Future<void> _disposeService(String serviceName) async {
    switch (serviceName) {
      case 'AuthService':
        await _ref.read(authServiceProvider).dispose();
        break;
      case 'DatabaseService':
        await _ref.read(databaseServiceProvider).dispose();
        break;
      case 'LocationService':
        await _ref.read(locationServiceProvider).dispose();
        break;
      case 'NotificationService':
        await _ref.read(notificationServiceProvider).dispose();
        break;
      case 'PrayerTimesService':
        await _ref.read(prayerTimesServiceProvider).dispose();
        break;
      // Add other services as needed
    }
  }

  /// Get service health status
  Map<String, bool> getServiceHealth() {
    return Map.from(_serviceStates);
  }

  /// Check if all services are healthy
  bool get allServicesHealthy {
    return _serviceStates.values.every((isHealthy) => isHealthy);
  }

  /// Get initialization status
  bool get isInitialized => _isInitialized;
}

/// Service initialization exception
class ServiceInitializationException implements Exception {
  const ServiceInitializationException(this.message);

  final String message;

  @override
  String toString() => 'ServiceInitializationException: $message';
}
