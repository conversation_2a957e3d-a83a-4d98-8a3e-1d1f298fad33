import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../core/utils/app_logger.dart';

part 'notification_settings_provider.g.dart';

/// Enum for notification timing options
enum NotificationTiming {
  /// Send notification before prayer time
  beforePrayer,

  /// Send notification at exact prayer time
  exactTime,
}

/// Notification settings class
class NotificationSettings {
  /// Whether notifications are enabled
  final bool notificationsEnabled;

  /// Map of prayer times to whether notifications are enabled for them
  final Map<String, bool> prayerNotificationsEnabled;

  /// Minutes before prayer time to send notification
  final int minutesBefore;

  /// Notification timing option (before prayer or at exact time)
  final NotificationTiming notificationTiming;

  /// Constructor
  const NotificationSettings({
    required this.notificationsEnabled,
    required this.prayerNotificationsEnabled,
    required this.minutesBefore,
    required this.notificationTiming,
  });

  /// Copy with method
  NotificationSettings copyWith({
    bool? notificationsEnabled,
    Map<String, bool>? prayerNotificationsEnabled,
    int? minutesBefore,
    NotificationTiming? notificationTiming,
  }) {
    return NotificationSettings(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      prayerNotificationsEnabled: prayerNotificationsEnabled ?? this.prayerNotificationsEnabled,
      minutesBefore: minutesBefore ?? this.minutesBefore,
      notificationTiming: notificationTiming ?? this.notificationTiming,
    );
  }
}

/// Modern @riverpod provider for notification settings
@riverpod
class NotificationSettingsNotifier extends _$NotificationSettingsNotifier {
  @override
  NotificationSettings build() {
    // Load settings asynchronously and return default settings initially
    _loadSettings();

    // Default settings
    return const NotificationSettings(
      notificationsEnabled: true,
      prayerNotificationsEnabled: {
        'Fajr': true,
        'Sunrise': false,
        'Dhuhr': true,
        'Asr': true,
        'Sunset': false,
        'Maghrib': true,
        'Isha': true,
        'Midnight': false,
      },
      minutesBefore: 15,
      notificationTiming: NotificationTiming.exactTime,
    );
  }

  /// Load settings from SharedPreferences
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load notifications enabled
      final notificationsEnabled = prefs.getBool('notifications_enabled') ?? true;

      // Load prayer notifications enabled
      final prayerNotificationsEnabled = <String, bool>{};
      final defaultPrayerSettings = {
        'Fajr': true,
        'Sunrise': false,
        'Dhuhr': true,
        'Asr': true,
        'Sunset': false,
        'Maghrib': true,
        'Isha': true,
        'Midnight': false,
      };

      for (final prayer in defaultPrayerSettings.keys) {
        final enabled =
            prefs.getBool('prayer_notification_${prayer.toLowerCase()}') ?? defaultPrayerSettings[prayer] ?? false;
        prayerNotificationsEnabled[prayer] = enabled;
      }

      // Load minutes before
      final minutesBefore = prefs.getInt('notification_minutes_before') ?? 15;

      // Load notification timing preference
      final notificationTimingIndex = prefs.getInt('notification_timing') ?? 1; // Default to exactTime (index 1)
      final notificationTiming = NotificationTiming.values[notificationTimingIndex];

      // Update state
      state = NotificationSettings(
        notificationsEnabled: notificationsEnabled,
        prayerNotificationsEnabled: prayerNotificationsEnabled,
        minutesBefore: minutesBefore,
        notificationTiming: notificationTiming,
      );

      AppLogger.debug('NotificationSettingsNotifier: Settings loaded');
    } on Exception catch (e) {
      AppLogger.error('NotificationSettingsNotifier: Error loading settings - $e');
    }
  }

  /// Save settings to SharedPreferences
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final currentState = state;

      // Save notifications enabled
      await prefs.setBool('notifications_enabled', currentState.notificationsEnabled);

      // Save prayer notifications enabled
      for (final entry in currentState.prayerNotificationsEnabled.entries) {
        await prefs.setBool('prayer_notification_${entry.key.toLowerCase()}', entry.value);
      }

      // Save minutes before
      await prefs.setInt('notification_minutes_before', currentState.minutesBefore);

      // Save notification timing preference
      await prefs.setInt('notification_timing', currentState.notificationTiming.index);

      AppLogger.debug('NotificationSettingsNotifier: Settings saved');
    } on Exception catch (e) {
      AppLogger.error('NotificationSettingsNotifier: Error saving settings - $e');
    }
  }

  /// Toggle notifications enabled
  Future<void> toggleNotificationsEnabled() async {
    state = state.copyWith(notificationsEnabled: !state.notificationsEnabled);
    await _saveSettings();
  }

  /// Toggle prayer notification enabled
  Future<void> togglePrayerNotification(String prayer) async {
    final currentValue = state.prayerNotificationsEnabled[prayer] ?? false;
    final updatedMap = Map<String, bool>.from(state.prayerNotificationsEnabled);
    updatedMap[prayer] = !currentValue;

    state = state.copyWith(prayerNotificationsEnabled: updatedMap);
    await _saveSettings();
  }

  /// Set minutes before prayer
  Future<void> setMinutesBefore(int minutes) async {
    state = state.copyWith(minutesBefore: minutes);
    await _saveSettings();
  }

  /// Enable all prayer notifications
  Future<void> enableAllPrayerNotifications() async {
    final updatedMap = Map<String, bool>.from(state.prayerNotificationsEnabled);
    for (final key in updatedMap.keys) {
      updatedMap[key] = true;
    }

    state = state.copyWith(prayerNotificationsEnabled: updatedMap);
    await _saveSettings();
  }

  /// Disable all prayer notifications
  Future<void> disableAllPrayerNotifications() async {
    final updatedMap = Map<String, bool>.from(state.prayerNotificationsEnabled);
    for (final key in updatedMap.keys) {
      updatedMap[key] = false;
    }

    state = state.copyWith(prayerNotificationsEnabled: updatedMap);
    await _saveSettings();
  }

  /// Set notification timing option
  Future<void> setNotificationTiming(NotificationTiming timing) async {
    state = state.copyWith(notificationTiming: timing);
    await _saveSettings();
  }
}
