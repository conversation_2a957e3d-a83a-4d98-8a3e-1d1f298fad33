import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:integration_test/integration_test.dart';

import 'package:masajid_albahrain/core/providers/theme/theme_settings_provider.dart';
// Context7 MCP: Updated to use unified notification settings provider
import 'package:masajid_albahrain/core/notifications/providers/unified_notification_provider.dart';
import 'package:masajid_albahrain/core/providers/prayer_times/prayer_times_settings_provider.dart';
import 'package:masajid_albahrain/core/providers/location/location_settings_provider.dart';
import 'package:masajid_albahrain/core/providers/performance/performance_settings_provider.dart';
import 'package:masajid_albahrain/core/providers/advanced/advanced_settings_provider.dart';

/// Performance testing suite for provider splitting implementation
/// Following Context7 MCP best practices for comprehensive performance validation
class ProviderSplittingPerformanceTest {
  static const String testTag = 'performance';

  /// Performance metrics collection
  static final Map<String, List<double>> _metrics = {};

  /// Test configuration
  static const int warmupIterations = 5;
  static const int measurementIterations = 20;
  static const Duration testTimeout = Duration(minutes: 5);

  /// Performance thresholds (in milliseconds)
  static const double maxRebuildTime = 16.0; // 60fps target
  static const double maxSerializationTime = 10.0;
  static const double maxProviderCreationTime = 5.0;
  static const double maxStateUpdateTime = 8.0;
}

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Provider Splitting Performance Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    testWidgets('Provider Creation Performance', (WidgetTester tester) async {
      final stopwatch = Stopwatch();
      final creationTimes = <double>[];

      // Warmup
      for (int i = 0; i < ProviderSplittingPerformanceTest.warmupIterations; i++) {
        final warmupContainer = ProviderContainer();
        warmupContainer.read(themeSettingsProvider);
        warmupContainer.dispose();
      }

      // Measure provider creation times
      for (int i = 0; i < ProviderSplittingPerformanceTest.measurementIterations; i++) {
        final testContainer = ProviderContainer();

        stopwatch.reset();
        stopwatch.start();

        // Create all domain-specific providers
        testContainer.read(themeSettingsProvider);
        testContainer.read(notificationSettingsProvider);
        testContainer.read(prayerTimesSettingsProvider);
        testContainer.read(locationSettingsProvider);
        testContainer.read(performanceSettingsProvider);
        testContainer.read(advancedSettingsProvider);

        stopwatch.stop();
        creationTimes.add(stopwatch.elapsedMicroseconds / 1000.0);

        testContainer.dispose();
      }

      final averageCreationTime = creationTimes.reduce((a, b) => a + b) / creationTimes.length;
      final maxCreationTime = creationTimes.reduce((a, b) => a > b ? a : b);

      debugPrint('📊 Provider Creation Performance:');
      debugPrint('   Average: ${averageCreationTime.toStringAsFixed(2)}ms');
      debugPrint('   Maximum: ${maxCreationTime.toStringAsFixed(2)}ms');
      debugPrint('   Threshold: ${ProviderSplittingPerformanceTest.maxProviderCreationTime}ms');

      expect(
        averageCreationTime,
        lessThan(ProviderSplittingPerformanceTest.maxProviderCreationTime),
        reason: 'Provider creation time exceeds threshold',
      );
      expect(
        maxCreationTime,
        lessThan(ProviderSplittingPerformanceTest.maxProviderCreationTime * 2),
        reason: 'Maximum provider creation time exceeds threshold',
      );
    }, timeout: ProviderSplittingPerformanceTest.testTimeout);

    testWidgets('State Update Performance', (WidgetTester tester) async {
      final stopwatch = Stopwatch();
      final updateTimes = <double>[];

      // Warmup
      for (int i = 0; i < ProviderSplittingPerformanceTest.warmupIterations; i++) {
        container.read(themeSettingsProvider.notifier).updateThemeMode(ThemeMode.dark);
      }

      // Measure state update times
      for (int i = 0; i < ProviderSplittingPerformanceTest.measurementIterations; i++) {
        stopwatch.reset();
        stopwatch.start();

        // Update different provider states
        container.read(themeSettingsProvider.notifier).updateThemeMode(i % 2 == 0 ? ThemeMode.dark : ThemeMode.light);

        stopwatch.stop();
        updateTimes.add(stopwatch.elapsedMicroseconds / 1000.0);
      }

      final averageUpdateTime = updateTimes.reduce((a, b) => a + b) / updateTimes.length;
      final maxUpdateTime = updateTimes.reduce((a, b) => a > b ? a : b);

      debugPrint('📊 State Update Performance:');
      debugPrint('   Average: ${averageUpdateTime.toStringAsFixed(2)}ms');
      debugPrint('   Maximum: ${maxUpdateTime.toStringAsFixed(2)}ms');
      debugPrint('   Threshold: ${ProviderSplittingPerformanceTest.maxStateUpdateTime}ms');

      expect(
        averageUpdateTime,
        lessThan(ProviderSplittingPerformanceTest.maxStateUpdateTime),
        reason: 'State update time exceeds threshold',
      );
    }, timeout: ProviderSplittingPerformanceTest.testTimeout);

    testWidgets('Memory Usage Validation', (WidgetTester tester) async {
      final initialMemory = _getCurrentMemoryUsage();
      final containers = <ProviderContainer>[];

      // Create multiple provider containers to test memory usage
      for (int i = 0; i < 100; i++) {
        final testContainer = ProviderContainer();

        // Initialize all providers
        testContainer.read(themeSettingsProvider);
        testContainer.read(notificationSettingsProvider);
        testContainer.read(prayerTimesSettingsProvider);
        testContainer.read(locationSettingsProvider);
        testContainer.read(performanceSettingsProvider);
        testContainer.read(advancedSettingsProvider);

        containers.add(testContainer);
      }

      final peakMemory = _getCurrentMemoryUsage();
      final memoryIncrease = peakMemory - initialMemory;

      // Dispose all containers
      for (final container in containers) {
        container.dispose();
      }

      // Force garbage collection
      await tester.binding.delayed(const Duration(milliseconds: 100));

      final finalMemory = _getCurrentMemoryUsage();
      final memoryLeakage = finalMemory - initialMemory;

      debugPrint('📊 Memory Usage Analysis:');
      debugPrint('   Initial: ${(initialMemory / 1024 / 1024).toStringAsFixed(2)}MB');
      debugPrint('   Peak: ${(peakMemory / 1024 / 1024).toStringAsFixed(2)}MB');
      debugPrint('   Final: ${(finalMemory / 1024 / 1024).toStringAsFixed(2)}MB');
      debugPrint('   Increase: ${(memoryIncrease / 1024 / 1024).toStringAsFixed(2)}MB');
      debugPrint('   Leakage: ${(memoryLeakage / 1024 / 1024).toStringAsFixed(2)}MB');

      // Validate memory usage is reasonable
      expect(
        memoryIncrease,
        lessThan(50 * 1024 * 1024), // Less than 50MB increase
        reason: 'Memory usage increase is too high',
      );
      expect(
        memoryLeakage,
        lessThan(5 * 1024 * 1024), // Less than 5MB leakage
        reason: 'Memory leakage detected',
      );
    }, timeout: ProviderSplittingPerformanceTest.testTimeout);

    testWidgets('Rebuild Frequency Optimization', (WidgetTester tester) async {
      int rebuildCount = 0;

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Consumer(
              builder: (context, ref, child) {
                rebuildCount++;

                // Watch only theme settings
                final themeSettings = ref.watch(themeSettingsProvider);

                return Scaffold(body: Text('Theme: ${themeSettings.themeMode}'));
              },
            ),
          ),
        ),
      );

      // Reset rebuild count after initial build
      rebuildCount = 0;

      // Update theme settings - should trigger rebuild
      container.read(themeSettingsProvider.notifier).updateThemeMode(ThemeMode.dark);
      await tester.pump();

      expect(rebuildCount, equals(1), reason: 'Theme update should trigger exactly one rebuild');

      // Update notification settings - should NOT trigger rebuild
      rebuildCount = 0;
      container.read(notificationSettingsProvider.notifier).updatePrayerNotifications(false);
      await tester.pump();

      expect(rebuildCount, equals(0), reason: 'Notification update should not trigger theme consumer rebuild');

      debugPrint('✅ Rebuild frequency optimization validated');
    }, timeout: ProviderSplittingPerformanceTest.testTimeout);
  }, tags: [ProviderSplittingPerformanceTest.testTag]);
}

/// Helper function to get current memory usage
int _getCurrentMemoryUsage() {
  if (kIsWeb) {
    // Web platform doesn't support ProcessInfo
    return 0;
  }

  try {
    return ProcessInfo.currentRss;
  } catch (e) {
    // Fallback for platforms that don't support ProcessInfo
    return 0;
  }
}
